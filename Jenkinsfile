pipeline {
   agent any
   environment {
       def tag = VersionNumber(versionNumberString: 'v${BUILD_DATE_FORMATTED, "yyMMdd"}${BUILDS_TODAY}')
       def slackToken = "${SLACK_TOKEN}"
       // Global variable from jen<PERSON>
       def imageRegistryHost = "${IMAGE_REGISTRY_HOST}"
       def imageRegistry = "${IMAGE_REGISTRY}"
       def jenkinsEnv = "${JENKINS_ENV}"
       def branchName = "${GIT_BRANCH.split("/")[1]}"
       def namespace = "icarus-${branchName}"
       def appName = "be-api-marketplaces"
       def registry = "${namespace}/${appName}"
       def envFile = "/var/lib/jenkins/config/icarus/be/${branchName}.env"
   }
   stages {
       stage('Build') {
           environment {
               def registryCredential = 'tbsi-jfrog'
           }
           steps{
               script {
                   sh "infisical export --env ${namespace} --projectId ${INFISICAL_PROJECT_ID} --token ${INFISICAL_TOKEN} --domain=${INFISICAL_API_URL} > .env"
                   sh "infisical export --env ${namespace} --projectId ${INFISICAL_PROJECT_ID} --token ${INFISICAL_TOKEN} --domain=${INFISICAL_API_URL} --path /${appName} >> .env"
                   sh "curl -s -X POST https://hooks.slack.com/services/" + slackToken + " -H 'Content-Type: application/json' -d '{\"text\": \"⛏ Building image ${registry}:${tag}\n\n🌍 ENV: ${jenkinsEnv}\n📛 NS: ${namespace}\n♨ Repo: ${GIT_URL}\n🌿 Branch: ${branchName}\n💬 Commit: ${GIT_COMMIT}\"}'"
                   def appimage = docker.build registry + ":" + tag
                   sh "rm -Rf .env"
                   docker.withRegistry( imageRegistry, registryCredential ) {
                       sh "curl -s -X POST https://hooks.slack.com/services/" + slackToken + " -H 'Content-Type: application/json' -d '{\"text\": \"📌 Pushing image ${registry}:${tag}\n\n🌍 ENV: ${jenkinsEnv}\n📛 NS: ${namespace}\n♨ Repo: ${GIT_URL}\n🌿 Branch: ${branchName}\n💬 Commit: ${GIT_COMMIT}\"}'"
                       appimage.push()
                       appimage.push('latest')
                   }
               }
           }
       }
       stage ('Deploy') {
           steps {
               script{
                   sh "curl -s -X POST https://hooks.slack.com/services/" + slackToken + " -H 'Content-Type: application/json' -d '{\"text\": \"🚀 Deployed image ${registry}:${tag} to Kubernetes Cluster (${K8S_CLUSTER})\n\n🌍 ENV: ${jenkinsEnv}\n📛 NS: ${namespace}\n♨ Repo: ${GIT_URL}\n🌿 Branch: ${branchName}\n💬 Commit: ${GIT_COMMIT}\"}'"
                   def image_id = registry + ":" + tag
                   sh "ansible-playbook playbook.yml --extra-vars \"image_id=${image_id} image_registry=${imageRegistryHost} namespace=${namespace}\""
               }
           }
       }
   }
 
   post {
        failure {
            sh "curl -s -X POST https://hooks.slack.com/services/" + slackToken + " -H 'Content-Type: application/json' -d '{\"text\": \"❌ Pipeline for image ${registry}:${tag} has failed, please check Jenkins UI for complete pipeline log\n\n🌍 ENV: ${jenkinsEnv}\n📛 NS: ${namespace}\n🌿 Branch: ${branchName}\n♨ Repo: ${GIT_URL}\"}'"   
        }
    }
}
