{"name": "nestjs-elastic-apm", "version": "1.2.0", "description": "NestJs Elastic APM Module", "directories": {"lib": "lib"}, "scripts": {"build": "rm -rf dist && tsc -p tsconfig.json", "precommit": "lint-staged", "publish:npm": "npm run build && npm publish"}, "repository": {"type": "git", "url": "git+https://github.com/mprimc/nestjs-elastic-apm"}, "keywords": ["NestJS", "Elastic", "APM"], "author": "<PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/mprimc/nestjs-elastic-apm/issues"}, "homepage": "https://github.com/mprimc/nestjs-elastic-apm#readme", "dependencies": {"elastic-apm-node": "^3.37.0"}, "lint-staged": {"*.ts": ["prettier --write", "git add"]}, "devDependencies": {"@nestjs/common": "^9.0.6", "@nestjs/core": "^9.0.6", "@types/node": "^18.6.2", "husky": "^8.0.1", "lint-staged": "^13.0.3", "prettier": "^2.7.1", "typescript": "^4.7.4", "rxjs": "^7.5.6"}}