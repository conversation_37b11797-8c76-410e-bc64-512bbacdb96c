"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.apm = void 0;
const apmAgent = require("elastic-apm-node");
require('dotenv').config()
const options = {
    active: process.env.ELASTIC_APM_ACTIVATE,
    logLevel: process.env.ELASTIC_APM_LOG_LEVEL,
    captureBody: process.env.ELASTIC_APM_CAPTURE_BODY,
    serviceName: process.env.ELASTIC_APM_SERVICE_NAME,
    secretToken: process.env.ELASTIC_APM_SECRET_TOKEN,
    apiKey: process.env.ELASTIC_APM_API_KEY,
    serverUrl: process.env.ELASTIC_APM_SERVER_URL,
    verifyServerCert: false,
    ignoreUrls: ['/api/v1/health-check'],
    environment: process.env.NODE_ENV
};
console.log({apmConfig: options})
const apm = apmAgent.start(options);
exports.apm = apm;
