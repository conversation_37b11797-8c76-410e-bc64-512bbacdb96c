import apm = require('elastic-apm-node');
export declare class ApmService {
    private readonly apm;
    constructor();
    captureError(data: any): void;
    startTransaction(name?: string, options?: apm.TransactionOptions): apm.Transaction | null;
    setTransactionName(name: string): void;
    startSpan(name?: string, options?: apm.SpanOptions): apm.Span | null;
    setCustomContext(context: Record<string, unknown>): void;
}
