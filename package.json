{"name": "be-boilerplate-v2", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:local": "cross-env NODE_ENV=local nest start --watch", "start:dev": "cross-env NODE_ENV=development nest start --watch", "start:debug": "cross-env NODE_ENV=development nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --no-fix --max-warnings 0", "test": "cross-env NODE_ENV=testing jest", "test:watch": "cross-env NODE_ENV=testing jest --watch", "test:cov": "cross-env NODE_ENV=testing jest --coverage", "test:debug": "cross-env NODE_ENV=testing node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "cross-env NODE_ENV=testing jest --config ./test/jest-e2e.json --forceExit", "prepare": "run-script-os", "prepare:darwin:linux": "husky install && chmod +x .husky/*", "prepare:default": "husky install", "deploy": "./deploy.sh utils 1.0.0"}, "dependencies": {"@aws-sdk/client-s3": "^3.441.0", "@liaoliaots/nestjs-redis": "^9.0.5", "@nestjs/axios": "^0.1.0", "@nestjs/common": "^9.0.8", "@nestjs/config": "^2.2.0", "@nestjs/core": "^9.0.8", "@nestjs/event-emitter": "^1.3.1", "@nestjs/mapped-types": "*", "@nestjs/mongoose": "^9.2.0", "@nestjs/platform-express": "^9.0.8", "@nestjs/schedule": "^2.1.0", "@nestjs/swagger": "^6.0.5", "@nestjs/terminus": "^9.1.0", "aws-sdk": "^2.1184.0", "class-transformer": "^0.5.1", "class-validator": "^0.13.2", "cross-env": "^7.0.3", "crypto-js": "^4.1.1", "dayjs": "^1.11.5", "dotenv": "^16.0.3", "elastic-apm-node": "^3.40.1", "isomorphic-fetch": "^3.0.0", "joi": "^17.6.0", "jwt-decode": "^3.1.2", "keycloak-connect-tbs": "^1.0.17", "mongoose": "^6.4.0", "mongoose-paginate-v2": "^1.7.0", "read-excel-file": "^5.5.3", "reflect-metadata": "^0.1.13", "rimraf": "^3.0.2", "rxjs": "^7.2.0", "swagger-ui-express": "^4.4.0", "tbs-site-config": "^0.0.7", "timezones-list": "^3.0.1", "write-excel-file": "^1.4.19"}, "devDependencies": {"@nestjs/cli": "^9.0.0", "@nestjs/schematics": "^9.0.1", "@nestjs/testing": "^9.0.8", "@types/express": "^4.17.13", "@types/jest": "27.5.0", "@types/multer": "^1.4.7", "@types/node": "^16.11.41", "@types/supertest": "^2.0.11", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "husky": "^8.0.1", "jest": "28.0.3", "prettier": "^2.3.2", "run-script-os": "^1.1.6", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "28.0.1", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "4.0.0", "typescript": "^4.3.5"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}