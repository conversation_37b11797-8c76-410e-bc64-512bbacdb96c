# Base image
FROM node:16-buster as builder

# Create app directory
WORKDIR /usr/src/app

# A wildcard is used to ensure both package.json AND package-lock.json are copied
COPY package*.json ./

# Install app dependencies
RUN --mount=type=cache,id=be-api-marketplaces-nm,target=/app/node_modules,uid=0,gid=0,mode=0777 npm install --ignore-scripts

# Bundle app source
COPY . .

# Creates a "dist" folder with the production build
RUN npm run build

FROM node:16-buster as runner

# Create app directory
WORKDIR /usr/src/app

COPY --from=builder /usr/src/app/node_modules ./node_modules
COPY --from=builder /usr/src/app/dist ./dist
COPY modules ./modules

COPY .env .env

# Start the server using the production build
# ENV NODE_ENV production

EXPOSE 3000
CMD [ "node", "dist/main.js" ]

