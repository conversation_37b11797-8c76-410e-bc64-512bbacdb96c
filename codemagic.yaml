workflows:
  testing:
    environment:
      node: lts
      npm: latest
    scripts:
      - name: Install Dependencies
        script: npm clean-install
      - name: Run Test
        script: npm test
    cache:
      cache_paths:
        - $CM_BUILD_DIR/node_modules
    publishing:
      email:
        recipients:
#          - ma<PERSON><PERSON><PERSON>-sa<PERSON><PERSON>@thebodyshop.co.id
          - <EMAIL>

