import { Module } from "@nestjs/common";
import { APP_GUARD } from "@nestjs/core";
import { MongooseModule, MongooseModuleOptions } from "@nestjs/mongoose";
import { ScheduleModule } from "@nestjs/schedule";
import {
  AuthGuard,
  InternalAccessGuard,
  KeycloakConnectModule,
  PolicyEnforcementMode,
  ResourceGuard,
  RoleGuard,
  TokenValidation,
} from "keycloak-connect-tbs";
import { TbsSiteConfigModule } from "tbs-site-config";
import { AppConfigModule } from "./common/app-config.module";
import { AppConfigService } from "./common/app-config.service";
import { CallbackModule } from "./module/callback/callback.module";
import { DcModule } from "./module/dc/dc.module";
import { ForstokModule } from "./module/forstok/forstok.module";
import { HealthCheckModule } from "./module/health-check/health-check.module";
import { KeycloakGeneratorModule } from "./module/keycloak-generator/keycloak-generator.module";
import { MarketplaceModule } from "./module/marketplace/marketplace.module";

@Module({
  imports: [
    KeycloakConnectModule.register({
      authServerUrl: process.env.KEYCLOAK_HOST,
      realm: process.env.KEYCLOAK_REALM,
      clientId: process.env.KEYCLOAK_CLIENTID,
      secret: process.env.KEYCLOAK_SECRET,
      policyEnforcement: PolicyEnforcementMode.PERMISSIVE,
      tokenValidation: TokenValidation.ONLINE,
      app_port: +process.env.PORT,
      bypass_iss_check: Boolean(process.env.KEYCLOAK_BYPASS_ISS),
      // Secret key of the client taken from keycloak server
    }),
    TbsSiteConfigModule.registerAsync({
      options: { host: process.env.REDIS_HOST, port: +process.env.REDIS_PORT, db: +process.env.REDIS_CONFIG_DB },
      prefix: process.env.KAFKA_TOPIC_PREFIX,
    }),
    MongooseModule.forRootAsync({
      imports: [AppConfigModule],
      inject: [AppConfigService],
      useFactory: async (appConfigService: AppConfigService) => {
        const uri = await appConfigService.connectionString;
        const options: MongooseModuleOptions = {
          uri: uri,
          dbName: "tbs_db_marketplaces",
          useNewUrlParser: true,
          useUnifiedTopology: true,
        };
        return options;
      },
    }),
    AppConfigModule,
    ForstokModule,
    HealthCheckModule,
    CallbackModule,
    KeycloakGeneratorModule,
    DcModule,
    ScheduleModule.forRoot(),
    MarketplaceModule,
  ],
  controllers: [],
  providers: [
    {
      provide: APP_GUARD,
      useClass: InternalAccessGuard,
    },
    {
      provide: APP_GUARD,
      useClass: AuthGuard,
    },
    {
      provide: APP_GUARD,
      useClass: ResourceGuard,
    },
    {
      provide: APP_GUARD,
      useClass: RoleGuard,
    },
  ],
})
export class AppModule {}
