/*
https://docs.nestjs.com/interceptors#interceptors
*/

import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from "@nestjs/common";
import { catchError, Observable } from "rxjs";
import { map } from "rxjs/operators";
import { rethrow } from "@nestjs/core/helpers/rethrow";

export interface Response {
  statusCode: number;
  message: string;
  data: any;
}

@Injectable()
export class ResponseInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const response = context.switchToHttp().getResponse();
    return next.handle().pipe(
      map((data) => {
        return {
          statusCode: response.statusCode,
          message: "Success",
          data: data,
        };
      }),
      catchError((err) => {
        console.log(err);
        rethrow(err);
      }),
    );
  }
}
