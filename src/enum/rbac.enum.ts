export enum Scope {
  POST = "create",
  PUT = "update",
  PATCH = "update",
  GET = "read",
  DELETE = "delete",
}

export enum Controllers {
  DC = "res:marketplaces:dc",
  FORSTOK_ADMIN = "res:marketplaces:forstok-admin",
  FORSTOK = "res:marketplaces:forstok",
  SHOPEE_STORE = "res:marketplaces:shopee-store",
  SHOPEE_AUTH = "res:marketplaces:shopee-auth",
  SHOPEE_PRODUCT = "res:marketplaces:shopee-product",
  SHOPEE_LOGISTIC = "res:marketplaces:shopee-logistic",
  SHOPEE_CATEGORY = "res:marketplaces:shopee-category",
  MARKETPLACE = "res:marketplaces:marketplace",
}
