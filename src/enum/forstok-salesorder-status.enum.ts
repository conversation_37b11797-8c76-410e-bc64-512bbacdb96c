export enum ForstokStatus {
  NotPaid = "Pending Payment",
  Open = "Open",
  Printed = "Printed",
  NotShipped = "Not Shipped",
  ReadyToShip = "Ready to Ship",
  Shipped = "Shipped",
  Delivered = "Delivered",
  Complete = "Complete",
  Cancelled = "Cancelled",
}

export enum MarketplaceStatus {
  NotPaid = "PENDING PAYMENT",
  Open = "OPEN",
  Printed = "PRINTED",
  NotShipped = "NOT SHIPPED",
  ReadyToShip = "READY TO SHIP",
  Shipped = "SHIPPED",
  Delivered = "DELIVERED",
  Complete = "COMPLETE",
  Cancelled = "CANCELLED",
}

export enum MarketplaceStatusPriority {
  "PENDING PAYMENT",
  "OPEN",
  "PRINTED",
  "NOT SHIPPED",
  "READY TO SHIP",
  "SHIPPED",
  "DELIVERED",
  "COMPLETE",
  "CANCELLED",
}

export enum ForstokOrderStatusPriority {
  NotPaid,
  Open,
  Printed,
  NotShipped,
  ReadyToShip,
  Shipped,
  Delivered,
  Complete,
  Cancelled,
}

export enum ForstokStatusPriority {
  "Pending Payment",
  "Open",
  "NotShipped",
  "Printed",
  "Not Shipped",
  "Ready to Ship",
  "Ready to Pick",
  "Order Prepared",
  "Shipped",
  "Delivered",
  "Complete",
  "Cancelled",
}

export enum DCOrderStatus {
  ReadyToPick = "Ready to Pick",
  OrderPrepared = "Order Prepared",
  ReadyToShip = "Ready to Ship",
  Shipped = "Shipped",
  Delivered = "Delivered",
  Complete = "Complete", // old one
  Completed = "Completed", // new one
  Cancelled = "Cancelled",
}

export enum ForstokChannel {
  Shopify = 2,
  Lazada = 3,
  Blanja = 5,
  Matahari_mall = 6,
  Magento_1 = 7,
  Elevenia = 8,
  Blibli = 9,
  Magento_2 = 10,
  Bukalapak = 11,
  Shopee = 12,
  Zalora_Indonesia = 13,
  Tokopedia = 15,
  JD_Indonesia = 16,
  Zilingo = 18,
  Woocommerce = 19,
  Sales_Order_Manual_SO = 21,
  Grabmart = 10017,
  Tiktok = 10030,
  Food_id = 10031,
  Aladin_Mall = 3532,
}

export enum OMSOrderStatus {
  Paid = "PAID",
  OnShipment = "ONSHIPMENT",
  OnProgress = "ONPROGRESS",
  ReadyToShip = "READY_TO_SHIP",
  Delivered = "DELIVERED",
  Complete = "COMPLETE",
  Cancelled = "CANCELLED",
}

export enum PaymentStatus {
  "Pending payment" = "Pending payment",
  "Pending payment verification" = "Pending payment verification",
  "Payment Verified" = "Payment Verified",
  Cancelled = "Cancelled",
  PAID = "PAID",
}

export enum OMSPaymentStatus {
  Pending = "Pending",
  Paid = "Paid",
  Failed = "Failed",
  Cancelled = "Cancelled",
}
