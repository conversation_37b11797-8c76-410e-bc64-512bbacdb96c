import * as dayjs from "dayjs";
import * as utc from "dayjs/plugin/utc";
import * as timezone from "dayjs/plugin/timezone";
import { isArray, isObject } from "class-validator";
import timezones from "timezones-list";
import { MimeTypeEnum } from "../enum/mime-type.enum";
import { HttpException } from "@nestjs/common";
import * as crypto from "crypto";

export const emptyCheck = (data) => [undefined, null, ""].includes(data);

export const genDateWithTimezone = (date: string, tz: string, startOfDay?: boolean, endOfDay?: boolean) => {
  dayjs.extend(utc);
  dayjs.extend(timezone);
  const split = tz.replace(/[\+-]/g, "").split(":");
  const hour = Number(split[0]);
  const min = Number(split[1]) || 0;
  let offset = hour * 60 + min;
  offset = eval(tz[0] + offset);

  let local = Intl.DateTimeFormat().resolvedOptions().timeZone;
  if (local === "UTC") {
    local = "Europe/London";
  }
  const localTz = timezones.find((data) => data.tzCode === local);
  const splitLocal = localTz.utc.replace(/[\+-]/g, "").split(":");
  const hourLocal = Number(splitLocal[0]);
  const minLocal = Number(splitLocal[1]) || 0;
  let offsetLocal = hourLocal * 60 + minLocal;
  offsetLocal = eval(tz[0] + offsetLocal);

  let format;
  if (offsetLocal < offset) {
    format = dayjs(date).utcOffset(offset);
  } else {
    format = dayjs(date).add(1, "day").utcOffset(offset);
  }

  if (endOfDay) {
    format = format.hour(23).minute(59).second(59).millisecond(999);
  }

  if (startOfDay) {
    if (tz[0] === "+") format = format.subtract(hour, "hour");
    else format = format.add(hour, "hour");
  }

  return format.toISOString();
};

const toSnakeCase = (s: string) => {
  return s
    .split(/\.?(?=[A-Z])/)
    .join("_")
    .toLowerCase();
};

export const keysToSnackCase = (o: any) => {
  if (isObject(o)) {
    const n = {};
    Object.keys(o).forEach((k) => {
      if (k === "_id") n[k] = o[k].toString();
      else n[toSnakeCase(k)] = keysToSnackCase(o[k]);
    });

    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    if (dayjs(o).isValid()) return o;

    return n;
  } else if (isArray(o)) {
    return o.map((i) => {
      return keysToSnackCase(i);
    });
  }
  return o;
};

export const wait = async (ms: number) => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};

export const validateImage = async (image: Record<string, any>, max_size: number) => {
  if (image) {
    const mimeType = Object.keys(MimeTypeEnum).filter((a) => a.match("/"));
    if (mimeType.indexOf(image.mimetype) === -1) {
      throw new HttpException(`Only ${mimeType.toString()} can be uploaded`, 400);
    }

    if (image.size / 1024 > max_size) {
      throw new HttpException(`Must lower than ${max_size}kb`, 400);
    }
  }

  return true;
};

export const convertByteString = (raw: string, toUnit: "b" | "kb" | "mb") => {
  const unitValue = {
    b: 1,
    kb: 1024,
    mb: 1024 * 1024,
  };
  const [value, unit] = raw.split(" ");

  if (!value && !unit) throw new Error("raw must be in string with space delimiter, Ex. 10 kb");
  if (isNaN(+value)) throw new Error("First text must be number");
  if (!unitValue[unit.toLowerCase()]) throw new Error("Unit from params not registered");

  return (+value * unitValue[unit.toLowerCase()]) / unitValue[toUnit];
};

export const shopeeTimestamp = () => Math.floor(Date.now() / 1000);

export const shopeeGenSign = (partnerKey: string, data: string) => {
  return crypto.createHmac("sha256", partnerKey).update(data).digest("hex");
};

export const combineTwoObject = (obj1: Record<string, any>, obj2: Record<string, any>, priority: 1 | 2) => {
  const data = { obj1, obj2 };

  let arr = 1;
  let combinator = 2;
  if (Object.keys(obj1).length < Object.keys(obj2).length) {
    arr = 2;
    combinator = 1;
  }

  Object.keys(data[`obj${arr}`]).map((k) => {
    const dataFormPriority = priority === 1 ? data[`obj1`][k] : data[`obj2`][k];
    const dataFromOther = priority === 1 ? data[`obj2`][k] : data[`obj1`][k];

    if (isArray(dataFormPriority)) {
      if (dataFromOther && !isArray(dataFromOther)) {
        throw new Error(`Incorrect data type for array in key ${k}`);
      }

      data[`obj${arr}`][k] = [...dataFormPriority, ...(dataFromOther || [])];
    } else if (isObject(dataFormPriority)) {
      if (dataFromOther && !isObject(dataFromOther)) {
        throw new Error(`Incorrect data type for object in key ${k}`);
      }

      data[`obj${arr}`][k] = { ...dataFormPriority, ...(dataFromOther || {}) };
    } else {
      data[`obj${arr}`][k] = dataFormPriority || dataFromOther;
    }
  });

  return { ...data[`obj${combinator}`], ...data[`obj${arr}`] };
};

export const stringToArray = (str: string, separator: string) => {
  return str?.split(separator)?.map((t) => String(t).trim());
};
