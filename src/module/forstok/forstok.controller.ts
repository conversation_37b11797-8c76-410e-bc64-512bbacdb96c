import { <PERSON>, Get, HttpException, HttpStatus, Post } from "@nestjs/common";
import { Public, Resource, RoleMatchingMode, Roles, Scopes } from "keycloak-connect-tbs";
import { <PERSON><PERSON>, Scope } from "src/enum/rbac.enum";
import { ApiBearerAuth, ApiTags } from "@nestjs/swagger";
import { ForstokService } from "./forstok.service";

@ApiTags("Admin - Forstok")
@Controller("admin/forstok")
@Roles({
  roles: ['admin', `realm:app-admin`],
  mode: RoleMatchingMode.ANY,
})
@Resource(Controllers.FORSTOK_ADMIN)
export class ForstokController {
  constructor(
    private readonly forstokService: ForstokService,
  ) {}

  @Get("forstok-order-error-list")
  @Scopes(Scope.GET)
  async listForStokError() {
    return await this.forstokService.listError();
  }

  @Post("push-order")
  @Scopes(Scope.POST)
  async pushOrders() {
    return await this.forstokService.pushOrders();
  }
}
