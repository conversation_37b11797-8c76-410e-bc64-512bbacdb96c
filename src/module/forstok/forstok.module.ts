import { Module } from "@nestjs/common";
import { ForstokService } from "./forstok.service";
import { ConfigModule } from "@nestjs/config";
import { UtilService } from "src/common/util.service";
import { HttpModule } from "@nestjs/axios";
import { ApmModule } from "../../../modules/nestjs-elastic-apm";
import { ForstokController } from "./forstok.controller";
import { MongooseModule } from "@nestjs/mongoose";
import { ForstokOrderError, ForstokOrderErrorSchema } from "../marketplace/schema/forstok-order-error.schema";

@Module({
  controllers: [ForstokController],
  providers: [ForstokService, UtilService],
  imports: [
    ConfigModule,
    HttpModule.register({
      timeout: 300000,
    }),
    MongooseModule.forFeature([
      {
        name: ForstokOrderError.name,
        schema: ForstokOrderErrorSchema,
      },
    ]),
    ApmModule.register(),
  ],
  exports: [ForstokService],
})
export class ForstokModule {}
