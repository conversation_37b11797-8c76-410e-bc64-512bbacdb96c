import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { lastValueFrom, map } from "rxjs";
import { UtilService } from "src/common/util.service";
import { HttpService } from "@nestjs/axios";
import * as dayjs from "dayjs";
import * as utc from "dayjs/plugin/utc";
import * as timezone from "dayjs/plugin/timezone";
import { AxiosRequestConfig } from "@nestjs/terminus/dist/health-indicator/http/axios.interfaces";
import { InjectModel } from "@nestjs/mongoose";
import { PaginateModel } from "mongoose";
import { ForstokOrderError, ForstokOrderErrorDocument } from "../marketplace/schema/forstok-order-error.schema";

interface ISetRts {
  token: string;
  orderId: number;
  courier?: string;
  awb?: string;
  pickupAddressId?: string;
  pickupTimeslotId?: string;
}
@Injectable()
export class ForstokService {
  constructor(
    private readonly httpService: HttpService,
    private configService: ConfigService,
    private readonly utilService: UtilService,
    @InjectModel(ForstokOrderError.name)
    private readonly forStokErrorModel: PaginateModel<ForstokOrderErrorDocument>,
  ) {}

  private forstokUrl = this.configService.get<string>("FORSTOK_ACCOUNTS_URL");
  private forstokAccount = this.configService.get<string>("FORSTOK_ACCOUNT");
  private forstokSecret = this.configService.get<string>("FORSTOK_SECRET");
  private authType = "jwt";

  //** Get Forstok Authorization Token */
  async getAuthToken() {
    try {
      const url = this.configService.get<string>("FORSTOK_INTEGRATION_URL");
      const requestUrl = url + "/v2/auth";
      const requestConfig: AxiosRequestConfig = {
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
        },
        params: {
          id: this.forstokAccount,
          secret_key: this.forstokSecret,
          type: "seller",
        },
      };
      const responseData = await lastValueFrom(
        this.httpService.post(requestUrl, null, requestConfig).pipe(
          map((response) => {
            return this.utilService.keysToCamel(response.data);
          }),
        ),
      );
      return responseData.data.token;
    } catch (e) {
      console.error(e);
      throw new HttpException("Error communicating to Forstok", HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  //** Pull Forstok Sales Orders */
  async pullOrders(start_date: string, end_date: string, batch: number) {
    const token = await this.getAuthToken();
    try {
      dayjs.extend(utc);
      dayjs.extend(timezone);
      const params = [
        "user_id=" + this.forstokAccount,
        "auth_type=" + this.authType,
        "updated_after=" + dayjs(start_date).tz("Asia/Jakarta").format("YYYY-MM-DDTHH:mm:ss.SSSZ"),
        "updated_before=" + dayjs(end_date).tz("Asia/Jakarta").format("YYYY-MM-DDTHH:mm:ss.SSSZ"),
        "limit=" + 15000,
        "offset=" + batch * 15000,
      ];

      const requestUrl = this.forstokUrl + "/v1/orders.json?" + params.join("&");
      const requestConfig: AxiosRequestConfig = {
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: "Bearer " + token,
        },
      };
      return await lastValueFrom(
        this.httpService.get(requestUrl, requestConfig).pipe(
          map((response) => {
            return { orders: response.data, count_order: response.data.length, token };
          }),
        ),
      );
    } catch (e) {
      console.error(e);
      throw new HttpException("Error communicating to Forstok", HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  //** Set the Forstok Sales Order status to Ready to Ship, i.e Request Pick Up */
  async setReadyToShip(params: ISetRts) {
    const { token, orderId, pickupAddressId, pickupTimeslotId, awb, courier } = params;

    try {
      const requestUrl = this.forstokUrl + "/v2/orders/fulfills_seller.json";
      const requestConfig: AxiosRequestConfig = {
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: "Bearer " + token,
        },
        params: {
          user_id: this.forstokAccount,
          auth_type: this.authType,
          order_id: orderId,
          tracking_company: courier,
          tracking_number: awb,
          pickup_address_id: pickupAddressId,
          pickup_timeslot_id: pickupTimeslotId,
        },
      };
      return await lastValueFrom(
        this.httpService.post(requestUrl, null, requestConfig).pipe(
          map((response) => {
            return this.utilService.keysToCamel(response.data);
          }),
        ),
      );
    } catch (e) {
      console.error(e);
      throw new HttpException("Error communicating to Forstok", HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  //** Forstok Confirm Order, only for Tokopedia, trigger Accept Order on Tokped */
  async confirmOrder(orderId: number, token: string) {
    try {
      const requestUrl = this.forstokUrl + "/v1/orders/confirm.json";
      const requestConfig: AxiosRequestConfig = {
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: "Bearer " + token,
        },
        data: {
          order_id: orderId,
        },
        params: {
          user_id: this.forstokAccount,
          auth_type: "jwt",
        },
      };
      return await lastValueFrom(
        this.httpService.post(requestUrl, null, requestConfig).pipe(
          map((response) => {
            return this.utilService.keysToCamel(response.data);
          }),
        ),
      );
    } catch (e) {
      console.error(e);
      throw new HttpException("Error communicating to Forstok", HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async getPickupAddress(orderId: number, token: string) {
    try {
      const requestUrl = this.forstokUrl + "/v1/orders/pickup_address.json";
      const requestConfig: AxiosRequestConfig = {
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: "Bearer " + token,
        },
        params: {
          user_id: this.forstokAccount,
          auth_type: "jwt",
          order_id: orderId,
        },
      };
      return await lastValueFrom(
        this.httpService.get(requestUrl, requestConfig).pipe(map((response) => response.data)),
      );
    } catch (e) {
      console.error(e);
      throw new HttpException("Error communicating to Forstok", HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async getPickupTimeslot(orderId: number, addressId: string, token: string) {
    try {
      const requestUrl = this.forstokUrl + "/v1/orders/pickup_timeslot.json";
      const requestConfig: AxiosRequestConfig = {
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: "Bearer " + token,
        },
        params: {
          user_id: this.forstokAccount,
          auth_type: "jwt",
          order_id: orderId,
          address_id: addressId,
        },
      };
      return await lastValueFrom(
        this.httpService.get(requestUrl, requestConfig).pipe(map((response) => response.data)),
      );
    } catch (e) {
      console.error(e);
      throw new HttpException("Error communicating to Forstok", HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async getDetail(id: number, token: string) {
    try {
      // const token = await this.getAuthToken();
      const url = this.forstokUrl + "/v1/orders/" + id + ".json";

      return await lastValueFrom(
        this.httpService
          .get(url, {
            headers: {
              Accept: "application/json",
              "Content-Type": "application/json",
              Authorization: "Bearer " + token,
            },
            params: { user_id: this.forstokAccount, auth_type: this.authType },
          })
          .pipe(map((res) => res.data)),
      );
    } catch (err) {
      console.log(err);
      throw new HttpException("Error communicating to Forstok", HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async registerCallback(event: string, url: string) {
    try {
      const token = await this.getAuthToken();
      const requestUrl = this.forstokUrl + "/v1/webhooks.json";

      return await lastValueFrom(
        this.httpService
          .post(
            requestUrl,
            {},
            {
              headers: {
                Accept: "application/json",
                "Content-Type": "application/json",
                Authorization: "Bearer " + token,
              },
              params: { user_id: this.forstokAccount, auth_type: this.authType, event, url },
            },
          )
          .pipe(map((res) => res.data)),
      );
    } catch (err) {
      console.log(err);
      throw new HttpException("Error communicating to Forstok", HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async getCallback() {
    try {
      const token = await this.getAuthToken();
      const requestUrl = this.forstokUrl + "/v1/webhooks.json";
      return await lastValueFrom(
        this.httpService
          .get(requestUrl, {
            headers: {
              Accept: "application/json",
              "Content-Type": "application/json",
              Authorization: "Bearer " + token,
            },
            params: { user_id: this.forstokAccount, auth_type: this.authType },
          })
          .pipe(map((res) => res.data)),
      );
    } catch (err) {
      console.log(err);
      throw new HttpException("Error communicating to Forstok", HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async getCallbackHistory(from: string, to: string, keyword?: string) {
    try {
      const token = await this.getAuthToken();
      const requestUrl = this.forstokUrl + "/v1/webhook_logs.json";
      return await lastValueFrom(
        this.httpService
          .get(requestUrl, {
            headers: {
              Accept: "application/json",
              "Content-Type": "application/json",
              Authorization: "Bearer " + token,
            },
            params: { user_id: this.forstokAccount, auth_type: this.authType, from, to, keyword },
          })
          .pipe(map((res) => res.data)),
      );
    } catch (err) {
      console.log(err);
      throw new HttpException("Error communicating to Forstok", HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async updateStock(body: Array<Record<string, any>>) {
    try {
      const token = await this.getAuthToken();
      const requestUrl = this.forstokUrl + "/v2/variants/quantities.json";
      return await lastValueFrom(
        this.httpService
          .patch(
            requestUrl,
            { variants: body },
            {
              headers: {
                Accept: "application/json",
                "Content-Type": "application/json",
                Authorization: "Bearer " + token,
              },
              params: { user_id: this.forstokAccount, auth_type: this.authType },
            },
          )
          .pipe(map((res) => res.data)),
      );
    } catch (err) {
      console.log(err);
      throw new HttpException("Error communicating to Forstok", HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async listError() {
    return await this.forStokErrorModel.find({ errorMessage: { $regex: "sudah habis" } }).sort({ forstokOrderId: -1 });
  }

  async pushOrders() {
    const forstok = await this.forStokErrorModel.find({ errorMessage: { $regex: "sudah habis" } });
    const forstokOrderIds = [];

    forstok.map((item) => {
      forstokOrderIds.push(item.forstokOrderId);
    });

    try {
      return await lastValueFrom(
        this.httpService
          .post(
            process.env.WEBHOOK_ICARUS_URL + "/webhook/reprocess-order-mp-oos",
            {
              forstokOrderIds: forstokOrderIds,
            },
            {
              headers: {
                Accept: "application/json",
                "Content-Type": "application/json",
              },
            },
          )
          .pipe(map((res) => res.data)),
      );
    } catch (err) {
      console.log(err);
      throw new HttpException("Error communicating to Workflow", HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
