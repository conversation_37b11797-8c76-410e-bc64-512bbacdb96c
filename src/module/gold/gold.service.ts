import { HttpService } from "@nestjs/axios";
import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import * as dayjs from "dayjs";
import { lastValueFrom } from "rxjs";
import { map } from "rxjs/operators";

@Injectable()
export class GoldService {
  constructor(private readonly configService: ConfigService, private readonly httpService: HttpService) {}

  private readonly _baseUrl = this.configService.get<string>("GOLD_HOST");
  private readonly _username = this.configService.get<string>("GOLD_USER");
  private readonly _pass = this.configService.get<string>("GOLD_PASS");
  private readonly _channelId = this.configService.get<string>("GOLD_CHANNEL_ID");

  async salesNoteCreate(orderNumber: string, items: Array<any>) {
    try {
      const methodName = "GWSAssistedSalesSalesNoteCreate";
      let xml = this._genHeader(methodName);
      xml += this._genOpeningBodyCreate(orderNumber);
      xml += this._genDetail(items);
      xml += this._genClosingBody(methodName);

      console.info(`REQUEST - GOLD Sales Note Create : ${xml}`);
      return await lastValueFrom(
        this.httpService
          .post(this._baseUrl + "/eretail/GWSAssistedSales", xml, {
            headers: { "Content-Type": "text/xml" },
          })
          .pipe(
            map((res) => {
              const salesNoteNumber = res.data.match(/<gws:salesNoteNumber>.*<\/gws:salesNoteNumber>/g);
              const wrap = salesNoteNumber[0]?.match(/\d+/);

              console.info(`RESPONSE - GOLD Sales Note Create : ${res.data}`);
              return wrap?.[0];
            }),
          ),
      );
    } catch (err) {
      console.dir({ title: "Error sales note create", msg: err.message, stack: err.stack });
      throw new HttpException(err, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async salesNoteSave(orderNumber: string, noteNumber: string, items: Array<any>) {
    try {
      const methodName = "GWSAssistedSalesSalesNoteSave";
      let xml = this._genHeader(methodName);
      xml += this._genOpeningBodySave(orderNumber, noteNumber);
      xml += this._genDetail(items);
      xml += this._genClosingBody(methodName);

      console.info(`REQUEST - GOLD Sales Note Save : ${xml}`);
      return await lastValueFrom(
        this.httpService
          .post(this._baseUrl + "/eretail/GWSAssistedSales", xml, {
            headers: { "Content-Type": "text/xml" },
          })
          .pipe(
            map((res) => {
              console.info(`RESPONSE - GOLD Sales Note Save : ${res.data}`);
              const priceGold = res.data.match(/<gws:salesPriceWithTax>.*<\/gws:salesPriceWithTax>/g);
              const wrap = priceGold?.[0]?.match(/\d+/);

              return Number(wrap?.[0]);
            }),
          ),
      );
    } catch (err) {
      console.dir({ title: "Error sales note save", msg: err.message, stack: err.stack });
      throw new HttpException(err, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async salesNoteCash(noteNumber: string, orderNumber: string, paymentAmount: string, date: string) {
    try {
      const methodName = "GWSAssistedSalesSalesNoteCash";
      let xml = this._genHeader(methodName);
      xml += this._genOpeningBodyCash(noteNumber, orderNumber);

      xml += "";
      xml += `
          <web:PaymentFooterDetail>
            <web:groupRecord recordState="1">
              <web:paymentModeCode>1</web:paymentModeCode>
              <web:paymentAmount>${paymentAmount}</web:paymentAmount>
              <web:paymentDate>${dayjs(date).format("YYYY-MM-DD")}</web:paymentDate>
              <web:paymentAuthorizationCode>1</web:paymentAuthorizationCode>
            </web:groupRecord>
          </web:PaymentFooterDetail>
      `;

      xml += this._genClosingBody(methodName);

      console.info(`REQUEST - GOLD Sales Note Cash : ${xml}`);
      // return xml;
      return await lastValueFrom(
        this.httpService
          .post(this._baseUrl + "/eretail/GWSAssistedSales", xml, {
            headers: { "Content-Type": "text/xml" },
          })
          .pipe(
            map((res) => {
              console.info(`RESPONSE - GOLD Sales Note Cash : ${res.data}`);
              const status = res.data.match(/<gws:salesNoteStatusDescription>.*<\/gws:salesNoteStatusDescription>/g);
              const wrap = status?.[0]?.match(/Completed/);

              return wrap?.[0];
            }),
          ),
      );
    } catch (err) {
      console.dir({ title: "Error sales note create", msg: err.msg, stack: err.stackTrace });
      throw new HttpException(err, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  private _genHeader(methodName) {
    return `<?xml version="1.0"?>
     <soapenv:Envelope
      xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
      xmlns:web="http://www.gold-solutions.com/WebServices/">
      <soapenv:Header>
        <web:GoldParameters>
          <web:GoldAuthentification>
            <web:Username>${this._username}</web:Username>
            <web:Password>${this._pass}</web:Password>
            <web:Domain>DEFAULT</web:Domain>
            <web:MethodName>${methodName}</web:MethodName>
          </web:GoldAuthentification>
          <web:GoldContext>
            <web:Classe>10</web:Classe>
            <web:Site>${this._channelId}</web:Site>
            <web:MerchStructID>1</web:MerchStructID>
            <web:CommercialNetworkID>1</web:CommercialNetworkID>
            <web:Language>GB</web:Language>
          </web:GoldContext>
        </web:GoldParameters>
      </soapenv:Header>`;
  }

  private _genOpeningBodyCreate(orderNumber: string) {
    return `
    <soapenv:Body>
      <web:Request_GWSAssistedSalesSalesNoteCreate>
        <web:Data>
          <web:SalesNotesHeader>
            <web:groupRecord>
              <web:userLogin>ecomsp</web:userLogin>
              <web:consumerCode>${this._channelId}</web:consumerCode>
              <web:consumerName>ecommerce</web:consumerName>
              <web:consumerFirstName>ecommerce</web:consumerFirstName>
              <web:comment250>${orderNumber}</web:comment250>
            </web:groupRecord>
          </web:SalesNotesHeader>`;
  }

  private _genOpeningBodySave(orderNumber: string, salesNote: string) {
    return `
    <soapenv:Body>
      <web:Request_GWSAssistedSalesSalesNoteSave>
        <web:Data>
        <web:SalesNotesHeader>
          <web:groupRecord>
            <web:userLogin>ecomsp</web:userLogin>
            <web:salesNoteNumber>${salesNote}</web:salesNoteNumber>
            <web:consumerCode>${this._channelId}</web:consumerCode>
            <web:consumerName>ecommerce</web:consumerName>
            <web:consumerFirstName>ecommerce</web:consumerFirstName>
            <web:comment250>${orderNumber}</web:comment250>
          </web:groupRecord>
        </web:SalesNotesHeader>`;
  }

  private _genOpeningBodyCash(noteNumber: string, orderNumber: string) {
    return `
    <soapenv:Body>
      <web:Request_GWSAssistedSalesSalesNoteCash>
        <web:Data>
          <web:SalesNotesHeader>
            <web:groupRecord>
              <web:salesNoteNumber>${noteNumber}</web:salesNoteNumber>
              <web:userLogin>ecomcsh</web:userLogin>
              <web:consumerCode>34999</web:consumerCode>
              <web:consumerName>ecommerce</web:consumerName>
              <web:consumerFirstName>ecommerce</web:consumerFirstName>
              <web:comment250>${orderNumber}</web:comment250>
              <web:deposit>0</web:deposit>
              <web:salesNotesPaiementModeCode>5</web:salesNotesPaiementModeCode>
            </web:groupRecord>
          </web:SalesNotesHeader>
    `;
  }

  private _genClosingBody(methodName: string) {
    return `
          </web:Data>
        </web:Request_${methodName}>
      </soapenv:Body>
    </soapenv:Envelope>`;
  }

  private _genDetail(items: Array<Record<string, any>>) {
    let xml = "<web:SalesNotesDetail>";
    items.map((item, i) => {
      xml += `
          <web:groupRecord recordState="1">
            <web:articleCode>${item.sku}</web:articleCode>
            <web:articleSUCode>${item.article_su_code}</web:articleSUCode>
            <web:lineNumber>${i + 1}</web:lineNumber>
            <web:salesQuantity>${item.qty}</web:salesQuantity>
            <web:salesOriginStockCode>1</web:salesOriginStockCode>
            <web:withdrawalModeCode>1</web:withdrawalModeCode>
            <web:withdrawalDate>${dayjs(item.orderedAt).format("YYYY-MM-DD")}</web:withdrawalDate>
            <web:carrierTypeForASCode>2</web:carrierTypeForASCode>
            <web:carrierInternalCode>2</web:carrierInternalCode>
            <web:geographicalAreaCode>1</web:geographicalAreaCode>
            <web:consumerWithdrawalAddressStreet1>Bintaro</web:consumerWithdrawalAddressStreet1>
            <web:consumerWithdrawalAddressTown>Tangerang Selatan</web:consumerWithdrawalAddressTown>
            <web:consumerWithdrawalAddressPostCode>154111</web:consumerWithdrawalAddressPostCode>
            <web:consumerWithdrawalAddressDistributingOffice>Taman Tekhno</web:consumerWithdrawalAddressDistributingOffice>
            <web:consumerWithdrawalAddressCountryCode>360</web:consumerWithdrawalAddressCountryCode>
            <web:comment250/>
          </web:groupRecord>
        `;
    });
    xml += "</web:SalesNotesDetail>";

    return xml;
  }
}
