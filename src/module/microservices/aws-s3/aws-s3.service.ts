import { Injectable } from "@nestjs/common";
import { ListObjectsV2Command, S3 } from "@aws-sdk/client-s3";

export interface IFetchFile {
  originalname: string;
  buffer: Buffer;
  mimetype: string;
  size: number;
  date: Date;
}

@Injectable()
export class AwsS3Service {
  private s3Client: S3;
  private bucket = process.env.AWS_S3_BUCKET;

  constructor() {
    this.s3Client = new S3({
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY,
        secretAccessKey: process.env.AWS_SECRET_KEY,
      },
      region: process.env.AWS_REGION,
    });
  }

  async getList(folder: string) {
    const command = new ListObjectsV2Command({ Bucket: this.bucket, Prefix: folder });
    const list = await this.s3Client.send(command);
    return list.Contents;
  }

  async fetchFile(key: string): Promise<IFetchFile> {
    const split = key.split("/");
    const obj = await this.s3Client.getObject({ Key: key, Bucket: this.bucket });

    return {
      originalname: split[split.length - 1],
      buffer: Buffer.from(await obj.Body.transformToByteArray()),
      mimetype: obj.ContentType,
      size: obj.ContentLength,
      date: obj.LastModified,
    };
  }
}
