/*
https://docs.nestjs.com/providers#services
*/

import { HttpException, Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { HttpService } from "@nestjs/axios";
import { AxiosRequestConfig } from "@nestjs/terminus/dist/health-indicator/http/axios.interfaces";
import { lastValueFrom, map } from "rxjs";

@Injectable()
export class ApiUsersService {
  constructor(private readonly httpService: HttpService, private configService: ConfigService) {}

  async getProfile(req) {
    const host = this.configService.get<string>("USERS_HOST");
    try {
      const requestUrl = host + "/api/v1/user/profile";
      const requestConfig: AxiosRequestConfig = {
        headers: {
          Authorization: req.headers.authorization,
        },
      };
      const responseData = await lastValueFrom(
        this.httpService.get(requestUrl, requestConfig).pipe(
          map((response) => {
            return response.data;
          }),
        ),
      );
      return responseData;
    } catch (e) {
      console.error(e.response);
      throw new HttpException(e.response.data.message, e.response.status);
    }
  }

  async getProfileAdmin(req) {
    const host = this.configService.get<string>("USERS_HOST");
    try {
      const requestUrl = host + "/api/v1/admin/profile";
      const requestConfig: AxiosRequestConfig = {
        headers: {
          Authorization: req.headers.authorization,
        },
      };
      const responseData = await lastValueFrom(
        this.httpService.get(requestUrl, requestConfig).pipe(
          map((response) => {
            return response.data;
          }),
        ),
      );
      return responseData;
    } catch (e) {
      console.error(e.response);
      throw new HttpException(e.response.data.message, e.response.status);
    }
  }
}
