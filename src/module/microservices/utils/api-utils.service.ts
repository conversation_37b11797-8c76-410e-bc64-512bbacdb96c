/*
https://docs.nestjs.com/providers#services
*/

import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { TbsSiteConfigService } from "tbs-site-config";

@Injectable()
export class ApiUtilsService {
  constructor(private configService: TbsSiteConfigService) {}

  // async test(counter, status): Promise<any> {
  //   return await lastValueFrom(
  //     this.httpService.post("http://localhost:3007/api/v1/callback/create", {
  //       counter,
  //       event: "orders/update",
  //       profile_id: 198,
  //       order: {
  //         id: 231322412,
  //         status: status,
  //         channel: "shopee",
  //         channel_id: 12,
  //         local_id: "230406DP9AT24V",
  //         local_name: "230406DP9AT24V",
  //         store_name: "Shopee",
  //         store_id: 1323,
  //         profile_id: 198,
  //         address: {
  //           address_1:
  //             "Serpong Garden Village Lumpang, Jalan Amaryllis, Lumpang, Parung Panjang (Amarilys no A04.50), KAB. BOGOR, PARUNG PANJANG, JAWA BARAT, ID, 16330",
  //           address_2: "",
  //           city: "KAB. BOGOR",
  //           country: "Indonesia",
  //           name: "S***n",
  //           phone: "******93",
  //           postal_code: "16330",
  //           province: "JAWA BARAT",
  //           province_code: "JAWA BARAT",
  //           sub_district: "",
  //           district: "PARUNG PANJANG",
  //           coordinate: null,
  //         },
  //         customer_info: {
  //           id: 43023664,
  //           name: "S***n",
  //           email: "",
  //           customer_since: "2021-09-05 13:05:13",
  //         },
  //         dropshipper_info: { id: 15678318, name: null, phone: null },
  //         ordered_at: "2023-04-06T15:56:53.000+07:00",
  //         created_at: "2023-04-06T15:56:57.000+07:00",
  //         updated_at: "2023-04-06T16:58:35.000+07:00",
  //         item_lines: [
  //           {
  //             id: 131540913,
  //             local_id: "0-*********",
  //             sku: "*********",
  //             name: "The Body Shop Strawberry Body Mist 100ml",
  //             variant_name: "The Body Shop Strawberry Body Mist 100ml",
  //             variant_id: 13680145,
  //             variant_sku: "*********",
  //             price: 199000,
  //             sale_price: 159200,
  //             total_price: 159200,
  //             voucher_amount: 0,
  //             voucher_code: null,
  //             voucher_seller: 0,
  //             tax_price: 0,
  //             fulfill_by_channel: false,
  //             shipping_provider: "Shopee Xpress Standard",
  //             shipping_provider_type: "Reguler (Cashless)",
  //             tracking_number: "SPXID038331718484",
  //             note: null,
  //             internal_note: null,
  //             bundle_info: [],
  //           },
  //           {
  //             id: *********,
  //             local_id: "1-*********",
  //             sku: "*********",
  //             name: "The Body Shop Strawberry Shower Gel 60ml",
  //             variant_name: "The Body Shop Strawberry Shower Gel 60ml",
  //             variant_id: 17790606,
  //             variant_sku: "*********",
  //             price: 49000,
  //             sale_price: 0,
  //             total_price: 49000,
  //             voucher_amount: 0,
  //             voucher_code: null,
  //             voucher_seller: 0,
  //             tax_price: 0,
  //             fulfill_by_channel: false,
  //             shipping_provider: "Shopee Xpress Standard",
  //             shipping_provider_type: "Reguler (Cashless)",
  //             tracking_number: "SPXID038331718484",
  //             note: null,
  //             internal_note: null,
  //             bundle_info: [],
  //           },
  //           {
  //             id: *********,
  //             local_id: "2-21919619390",
  //             sku: "*********",
  //             name: "The Body Shop New Strawberry Body Butter 50ml",
  //             variant_name: "The Body Shop New Strawberry Body Butter 50ml",
  //             variant_id: 13680107,
  //             variant_sku: "*********",
  //             price: 99000,
  //             sale_price: null,
  //             total_price: 99000,
  //             voucher_amount: 0,
  //             voucher_code: null,
  //             voucher_seller: 0,
  //             tax_price: 0,
  //             fulfill_by_channel: false,
  //             shipping_provider: "Shopee Xpress Standard",
  //             shipping_provider_type: "Reguler (Cashless)",
  //             tracking_number: "SPXID038331718484",
  //             note: null,
  //             internal_note: null,
  //             bundle_info: [],
  //           },
  //         ],
  //         payment: { payment_method: "Online Payment", status: "Payment Verified" },
  //         shipping_price: 10000,
  //         disc_shipping_seller: 0,
  //         disc_shipping_platform: 0,
  //         shipping_courier: {
  //           awb: "SPXID038331718484",
  //           document_path:
  //             "https://accounts.forstok.com/api/v1/documents.pdf?token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.******************************************************************************************************************************************.pISD-q3C5s79BS5C2JFa7TDbMdjL8P-Zw57fGliIiZ4",
  //           booking_code: null,
  //           delivery_type: "NON INTEGRATED",
  //           channel_docs_path: null,
  //           logistic_destination_code: "A-78",
  //         },
  //         shipping_provider: "Shopee Xpress Standard",
  //         shipping_provider_type: "Reguler (Cashless)",
  //         shipping_description: null,
  //         subtotal: 307200,
  //         channel_rebate: 0,
  //         cashless: true,
  //         discount_amount: 0,
  //         voucher_seller: 5000,
  //         total_price: 302200,
  //         voucher_code: "",
  //         insurance_fee: 0,
  //         discount_reason: null,
  //         tax_price: 0,
  //         warehouse_id: 195,
  //         cod: false,
  //         delivery_type: null,
  //         warehouse_code: null,
  //         note: "packing yang aman expired yang panjang kasih bonus thanks",
  //         internal_note: null,
  //         returns: [],
  //       },
  //     }),
  //   );
  // }

  async getConfigByPrefix(key: string) {
    try {
      const config = await this.configService.getByPattern(key);
      return {
        docs: config,
      };
    } catch (e) {
      console.error(e);
      throw new HttpException(e?.response?.data?.message || e, e?.response?.status || HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async getConfig(key: string, returnValueOnly = true) {
    try {
      const config = await this.configService.get(key);
      if (!config) {
        if (!returnValueOnly) {
          return {
            value: null,
            description: null,
            status: null,
          };
        }
        return;
      }
      const data = JSON.parse(config);

      if (!returnValueOnly) {
        return data;
      }

      return data.value;
    } catch (e) {
      if (e.code === "ERR_BAD_REQUEST") {
        return null;
      }
      console.error(e);

      throw new HttpException(e?.response?.data?.message || e, e?.response?.status || HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async updateConfig(key: string, value: any) {
    try {
      const previous = await this.configService.get(key);
      let mock = { value };

      if (previous) {
        mock = { ...JSON.parse(previous), ...mock };
      }

      await this.configService.updateValue(key, value);

      return mock;
    } catch (err) {
      return err.message;
    }
  }

  async createConfig(data: any) {
    try {
      return await this.configService.create(data);
    } catch (err) {
      return err.message;
    }
  }
}
