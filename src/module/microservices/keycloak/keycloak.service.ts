import { HttpService } from "@nestjs/axios";
import { Injectable } from "@nestjs/common";
import { lastValueFrom, map } from "rxjs";
import { ConfigService } from "@nestjs/config";
import { ICreatePermission } from "../../keycloak-generator/keycloak-generator.interface";

@Injectable()
export class KeycloakMSService {
  constructor(private readonly httpService: HttpService, private readonly configService: ConfigService) {}

  private readonly host = this.configService.get<string>("KEYCLOAK_HOST");
  private readonly realm = this.configService.get<string>("KEYCLOAK_REALM");
  private readonly clientId = this.configService.get<string>("KEYCLOAK_CLIENTID");
  private readonly clientSecret = this.configService.get<string>("KEYCLOAK_SECRET");
  private readonly baseUrl = this.host + "/realms/" + this.realm;
  private readonly wrapperHost = this.configService.get<string>("KEYCLOAK_WRAPPER_HOST");

  prefixRes = "res:marketplaces:";
  prefixPerm = "scope:marketplaces:";

  async getAllResources() {
    try {
      const url = this.baseUrl + "/authz/protection/resource_set";
      const token = await this._genPAT();
      return await lastValueFrom(
        this.httpService
          .get(url, { params: { name: this.prefixRes }, headers: { Authorization: token } })
          .pipe(map((response) => response.data)),
      );
    } catch (err) {
      console.log(err);
      throw err;
    }
  }

  async getDetailResources(id: string) {
    try {
      const url = this.baseUrl + "/authz/protection/resource_set/" + id;
      const token = await this._genPAT();
      return await lastValueFrom(
        this.httpService.get(url, { headers: { Authorization: token } }).pipe(map((response) => response.data)),
      );
    } catch (err) {
      console.log(err);
      throw err;
    }
  }

  async createResources(name: string, resource_scopes: Array<string>) {
    try {
      const url = this.baseUrl + "/authz/protection/resource_set";
      const token = await this._genPAT();
      return await lastValueFrom(
        this.httpService
          .post(url, { name, resource_scopes }, { headers: { Authorization: token } })
          .pipe(map((response) => response.data)),
      );
    } catch (err) {
      throw err;
    }
  }

  async getDetailClient() {
    try {
      const url = this.wrapperHost + "/api/v1/client/detail";
      const token = await this._genPAT();
      return await lastValueFrom(
        this.httpService
          .get(url, {
            params: { client_id: this.clientId, realm_name: this.realm },
            headers: { Authorization: token },
          })
          .pipe(map((response) => response.data?.data)),
      );
    } catch (err) {
      throw err;
    }
  }

  async getAllPermissions(client_id: string) {
    try {
      const url = this.wrapperHost + "/api/v1/permission";
      const token = await this._genPAT();
      return await lastValueFrom(
        this.httpService
          .get(url, {
            params: { client_id, page: 1, limit: 10000 },
            headers: { Authorization: token },
          })
          .pipe(map((response) => response.data?.data?.docs || [])),
      );
    } catch (err) {
      throw err;
    }
  }

  async createPermissions(body: ICreatePermission) {
    try {
      const mock = {
        ...body,
        type: "scope",
        decision_strategy: "0",
        logic: "0",
      };
      const url = this.wrapperHost + "/api/v1/permission";
      const token = await this._genPAT();
      return await lastValueFrom(
        this.httpService
          .post(url, mock, { headers: { Authorization: token } })
          .pipe(map((response) => response.data?.data || {})),
      );
    } catch (err) {
      console.log(err.response.data);
      throw err;
    }
  }

  async getAllScopes(client_id: string) {
    try {
      const url = this.wrapperHost + "/api/v1/scope/all-by-client/" + client_id;
      const token = await this._genPAT();
      return await lastValueFrom(
        this.httpService
          .get(url, { params: { client_id }, headers: { Authorization: token } })
          .pipe(map((response) => response.data?.data || [])),
      );
    } catch (err) {
      throw err;
    }
  }

  async getAllPolicy(client_id: string) {
    try {
      const url = this.wrapperHost + "/api/v1/permission/policy";
      const token = await this._genPAT();
      return await lastValueFrom(
        this.httpService
          .get(url, {
            params: { client_id, page: 1, limit: 10000 },
            headers: { Authorization: token },
          })
          .pipe(map((response) => response.data?.data?.docs || [])),
      );
    } catch (err) {
      throw err;
    }
  }

  async _genPAT() {
    try {
      const url = this.baseUrl + "/protocol/openid-connect/token";
      const params = {
        grant_type: "client_credentials",
        client_id: this.clientId,
        client_secret: this.clientSecret,
      };
      return await lastValueFrom(
        this.httpService
          .post(url, new URLSearchParams(params), {
            headers: { "Content-Type": "application/x-www-form-urlencoded" },
          })
          .pipe(map((response) => "Bearer " + response.data?.access_token)),
      );
    } catch (err) {
      throw err;
    }
  }
}
