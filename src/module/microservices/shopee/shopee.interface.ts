export interface IShopeeStoreUpdate {
  shop_name?: string;
  shop_logo?: string;
  description?: string;
}

export interface IShopeeCreateProduct {
  image: { image_id_list: Array<string> };
  video_upload_id: Array<string>;
  item_name: string;
  category_id: number;
  description: string;
  brand: { brand_id: number };
  original_price: number;
  seller_stock: Array<{ location_id?: string; stock: number }>;
  weight: number;
  dimension: { package_height: number; package_length: number; package_width: number };
  logistic_info: Array<{
    logistic_id: number;
    enabled: boolean;
    size_id?: number;
    shipping_fee?: number;
    is_free?: boolean;
  }>;
  condition: "NEW";
  item_sku: string;
  item_status: "NORMAL";
  wholesale: Array<{ min_count: number; max_count: number; unit_price: number }>;
}

export interface IShopeeUpdateProduct extends IShopeeCreateProduct {
  item_id: number;
}
