import { ShopeeApiAuthService } from "./shopee-auth.service";
import { ConfigService } from "@nestjs/config";
import { HttpService } from "@nestjs/axios";
import * as FormData from "form-data";
import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { shopeeGenSign, shopeeTimestamp } from "../../../utils/function.util";

@Injectable()
export class ShopeeApiUtilService {
  constructor(
    private readonly shopeeAuthService: ShopeeApiAuthService,
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
  ) {}

  private readonly baseUrl = this.configService.get<string>("SHOPEE_URL");
  private readonly partnerId = +this.configService.get<string>("SHOPEE_PARTNER_ID");
  private readonly partnerKey = this.configService.get<string>("SHOPEE_PARTNER_KEY");

  async uploadImage(image: Buffer, name: string) {
    const timestamp = shopeeTimestamp();
    const path = "/api/v2/media_space/upload_image";
    const sign = shopeeGenSign(this.partnerKey, this.partnerId + path + timestamp);

    const form = new FormData();
    form.append("image", image, name);

    const url = this.baseUrl + path;
    try {
      const res = await this.httpService.axiosRef.post(url, form, {
        headers: form.getHeaders(),
        params: { partner_id: this.partnerId, timestamp, sign },
      });

      if (res.data?.error) throw new HttpException(res.data?.message, HttpStatus.BAD_REQUEST);

      if (res.data.response?.image_info?.image_url_list) {
        const imageRes: Record<string, any> = {};
        for (const img of res.data.response.image_info.image_url_list) {
          if (img.image_url_region === "ID") {
            imageRes.url = img.image_url;
            imageRes.id = res.data.response?.image_info.image_id;
            break;
          }
        }
        return imageRes;
      }

      throw new HttpException("Error while upload image", HttpStatus.BAD_REQUEST);
    } catch (err) {
      console.log(err);
      throw new HttpException("Error while upload image", HttpStatus.BAD_REQUEST);
    }
  }
}
