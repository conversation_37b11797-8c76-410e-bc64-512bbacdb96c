import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { MarketplaceAuth, MarketplaceAuthDocument } from "../../marketplace/schema/auth.schema";
import { Model } from "mongoose";
import { ConfigService } from "@nestjs/config";
import { HttpService } from "@nestjs/axios";
import { ApiUtilsService } from "../utils/api-utils.service";
import * as dayjs from "dayjs";
import { shopeeGenSign, shopeeTimestamp } from "../../../utils/function.util";

@Injectable()
export class ShopeeApiAuthService {
  constructor(
    @InjectModel(MarketplaceAuth.name)
    private readonly marketplaceAuthModel: Model<MarketplaceAuthDocument>,
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
    private readonly utilService: ApiUtilsService,
  ) {}

  private readonly baseUrl = this.configService.get<string>("SHOPEE_URL");
  private readonly redirectUrl = this.configService.get<string>("SHOPEE_REDIRECT");
  private readonly partnerId = +this.configService.get<string>("SHOPEE_PARTNER_ID");
  private readonly partnerKey = this.configService.get<string>("SHOPEE_PARTNER_KEY");

  async genAuthorization() {
    const path = "/api/v2/shop/auth_partner";
    const timestamp = shopeeTimestamp();
    const sign = shopeeGenSign(this.partnerKey, this.partnerId + path + timestamp);

    return `${this.baseUrl}${path}?partner_id=${this.partnerId}&timestamp=${timestamp}&sign=${sign}&redirect=${this.redirectUrl}`;
  }

  async genAccessToken(shop_id: number, accessCode: string, main_account_id?: number) {
    const refreshTokenExp = await this.getRefreshTokenExp();
    const timestamp = shopeeTimestamp();
    const path = "/api/v2/auth/token/get";
    const body = {
      code: accessCode,
      partner_id: this.partnerId,
      shop_id,
      main_account_id,
    };
    const sign = shopeeGenSign(this.partnerKey, this.partnerId + path + timestamp);

    const url = this.baseUrl + path;
    try {
      const res = await this.httpService.axiosRef.post(url, body, {
        headers: { "Content-Type": "application/json" },
        params: { partner_id: this.partnerId, timestamp, sign },
      });

      if (res.data?.refresh_token) {
        return {
          refresh_token: res.data.refresh_token,
          access_token: res.data.access_token,
          refresh_token_exp: dayjs().add(refreshTokenExp, "day"),
          access_token_exp: dayjs().add(res.data.expire_in, "second"),
          store_id: res.data.shop_id_list?.[0] || shop_id,
        };
      }

      throw new HttpException("Error while generating access token", HttpStatus.BAD_REQUEST);
    } catch (err) {
      console.log(err);
      throw new HttpException("Error while generating access token", HttpStatus.BAD_REQUEST);
    }
  }

  async refreshToken(shop_id: number, refresh_token: string) {
    const refreshTokenExp = await this.getRefreshTokenExp();
    const timestamp = shopeeTimestamp();
    const path = "/api/v2/auth/access_token/get";
    const body = {
      partner_id: this.partnerId,
      refresh_token,
      shop_id,
    };
    const sign = shopeeGenSign(this.partnerKey, this.partnerId + path + timestamp);

    const url = this.baseUrl + path;
    try {
      const res = await this.httpService.axiosRef.post(url, body, {
        headers: { "Content-Type": "application/json" },
        params: { partner_id: this.partnerId, timestamp, sign },
      });

      if (res.data?.refresh_token) {
        return {
          refresh_token: res.data.refresh_token,
          access_token: res.data.access_token,
          refresh_token_exp: dayjs().add(refreshTokenExp, "day"),
          access_token_exp: dayjs().add(res.data.expire_in, "second"),
        };
      }

      throw new HttpException("Error while generating refresh token", HttpStatus.BAD_REQUEST);
    } catch (err) {
      console.log(err);
      throw new HttpException("Error while generating refresh token", HttpStatus.BAD_REQUEST);
    }
  }

  async getAccessToken(shop_id: string) {
    const token = await this.marketplaceAuthModel.findOne({ storeId: shop_id }).lean().exec();

    if (!token) {
      throw new HttpException("Store belum terdaftar atau belum melakukan autorisasi", HttpStatus.BAD_REQUEST);
    }

    if (token.accessTokenExp <= new Date()) {
      const refresh = await this.refreshToken(+shop_id, token.refreshToken);
      const newToken = await this.marketplaceAuthModel.findOneAndUpdate(
        { storeId: shop_id },
        {
          accessToken: refresh.access_token,
          accessTokenExp: refresh.access_token_exp,
          refreshToken: refresh.refresh_token,
          refreshTokenExp: refresh.refresh_token_exp,
        },
        { new: true },
      );

      return newToken.accessToken;
    }

    return token.accessToken;
  }

  async getRefreshTokenExp() {
    let refreshTokenExp = await this.utilService.getConfig("mp.shopee-refresh-token-exp", true);
    if (!refreshTokenExp) refreshTokenExp = "30";

    return +refreshTokenExp;
  }
}
