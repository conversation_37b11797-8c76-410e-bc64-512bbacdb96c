import { ShopeeApiAuthService } from "./shopee-auth.service";
import { CreateProductDto } from "../../marketplace/dto/create-product";
import { IShopeeCreateProduct, IShopeeUpdateProduct } from "./shopee.interface";
import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { HttpService } from "@nestjs/axios";
import { shopeeGenSign, shopeeTimestamp } from "../../../utils/function.util";
import { isArray } from "class-validator";
import { UpdateProductDto } from "../../marketplace/dto/update-product";

@Injectable()
export class ShopeeApiProductService {
  constructor(
    private readonly shopeeAuthService: ShopeeApiAuthService,
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
  ) {}

  private readonly baseUrl = this.configService.get<string>("SHOPEE_URL");
  private readonly partnerId = +this.configService.get<string>("SHOPEE_PARTNER_ID");
  private readonly partnerKey = this.configService.get<string>("SHOPEE_PARTNER_KEY");

  async create(shop_id: string, logistic: Array<Record<string, any>>, payload: CreateProductDto) {
    const wholesale = [];
    if (isArray(payload.wholesale)) {
      payload.wholesale.map((item) => {
        wholesale.push({
          min_count: item.minQty,
          max_count: item.maxQty,
          unit_price: item.price,
        });
      });
    }

    const mock: IShopeeCreateProduct = {
      category_id: payload.shopeeCategoryId,
      condition: "NEW",
      description: payload.description,
      item_name: payload.name,
      item_sku: payload.sku,
      item_status: "NORMAL",
      weight: payload.weight / 100,
      dimension: payload.dimension?.height
        ? {
            package_height: payload.dimension.height,
            package_length: payload.dimension.length,
            package_width: payload.dimension.width,
          }
        : null,
      wholesale: wholesale,
      original_price: payload.price,
      seller_stock: [{ stock: payload.stock }],
      brand: { brand_id: 0 },
      image: { image_id_list: payload.images.map((img) => img.id) },
      video_upload_id: payload.video?.id,
      logistic_info: logistic.map((l) => {
        return {
          logistic_id: l.logisticId,
          enabled: l.enabled,
          size_id: l.sizeList?.[0]?.size_id,
        };
      }),
    };

    const timestamp = shopeeTimestamp();
    const path = "/api/v2/product/add_item";
    const accessToken = await this.shopeeAuthService.getAccessToken(shop_id);

    const sign = shopeeGenSign(this.partnerKey, this.partnerId + path + timestamp + accessToken + shop_id);
    const url = this.baseUrl + path;
    try {
      const res = await this.httpService.axiosRef.post(url, mock, {
        params: { access_token: accessToken, partner_id: this.partnerId, timestamp, sign, shop_id },
      });

      if (res.data?.response?.item_id) return res.data.response;

      throw new HttpException("Error while create new product", HttpStatus.BAD_REQUEST);
    } catch (err) {
      console.log(err);
      throw new HttpException("Error while create new product", HttpStatus.BAD_REQUEST);
    }
  }

  async getList(shop_id: string, page = 1, size = 50) {
    const timestamp = shopeeTimestamp();
    const path = "/api/v2/product/get_item_list";
    const accessToken = await this.shopeeAuthService.getAccessToken(shop_id);

    const sign = shopeeGenSign(this.partnerKey, this.partnerId + path + timestamp + accessToken + shop_id);
    const url = this.baseUrl + path;
    try {
      const res = await this.httpService.axiosRef.get(url + "?item_status=NORMAL&item_status=UNLIST", {
        params: {
          access_token: accessToken,
          partner_id: this.partnerId,
          offset: (page - 1) * size,
          page_size: size,
          timestamp,
          sign,
          shop_id,
        },
      });

      if (res.data?.response?.item) return res.data.response;

      throw new HttpException("Error while get product list", HttpStatus.BAD_REQUEST);
    } catch (err) {
      console.log(err);
      throw new HttpException("Error while get product list", HttpStatus.BAD_REQUEST);
    }
  }

  async detail(shop_id: string, itemIds: Array<number>) {
    if (!itemIds.length) {
      throw new HttpException("ID Item harus terisi", HttpStatus.BAD_REQUEST);
    }

    const timestamp = shopeeTimestamp();
    const path = "/api/v2/product/get_item_base_info";
    const accessToken = await this.shopeeAuthService.getAccessToken(shop_id);

    const sign = shopeeGenSign(this.partnerKey, this.partnerId + path + timestamp + accessToken + shop_id);
    const url = this.baseUrl + path;
    try {
      const id = itemIds.map((i) => `item_id_list=${i}`);
      const res = await this.httpService.axiosRef.get(url + "?" + id.join("&"), {
        params: {
          access_token: accessToken,
          partner_id: this.partnerId,
          timestamp,
          sign,
          shop_id,
        },
      });

      if (res.data?.response?.item_list) return res.data.response.item_list;

      throw new HttpException("Error while get product list", HttpStatus.BAD_REQUEST);
    } catch (err) {
      console.log(err);
      throw new HttpException("Error while get product list", HttpStatus.BAD_REQUEST);
    }
  }

  async update(shop_id: string, payload: UpdateProductDto) {
    const wholesale = [];
    if (isArray(payload.wholesale)) {
      payload.wholesale.map((item) => {
        wholesale.push({
          min_count: item.minQty,
          max_count: item.maxQty,
          unit_price: item.price,
        });
      });
    }

    const mock: IShopeeUpdateProduct = {
      item_id: payload.itemId,
      category_id: payload.shopeeCategoryId,
      condition: "NEW",
      description: payload.description,
      item_name: payload.name,
      item_sku: payload.sku,
      item_status: "NORMAL",
      weight: payload.weight,
      dimension: payload.dimension?.height
        ? {
            package_height: payload.dimension.height,
            package_length: payload.dimension.length,
            package_width: payload.dimension.width,
          }
        : null,
      wholesale: wholesale,
      original_price: payload.oriPrice,
      seller_stock: [{ stock: payload.stock }],
      brand: { brand_id: 0 },
      image: { image_id_list: payload.images.map((img) => img.id) },
      video_upload_id: payload.video?.id,
      logistic_info: payload.logistic.map((l) => {
        return {
          logistic_id: l.logisticId,
          enabled: l.enabled,
          size_id: l.sizeList?.[0]?.size_id ? +l.sizeList?.[0]?.size_id : null,
        };
      }),
    };

    const timestamp = shopeeTimestamp();
    const path = "/api/v2/product/update_item";
    const accessToken = await this.shopeeAuthService.getAccessToken(shop_id);

    const sign = shopeeGenSign(this.partnerKey, this.partnerId + path + timestamp + accessToken + shop_id);
    const url = this.baseUrl + path;
    try {
      const res = await this.httpService.axiosRef.post(url, mock, {
        params: { access_token: accessToken, partner_id: this.partnerId, timestamp, sign, shop_id },
      });

      if (res.data?.response?.item_id) return res.data.response;

      throw new HttpException("Error while update product", HttpStatus.BAD_REQUEST);
    } catch (err) {
      console.log(err);
      throw new HttpException("Error while update product", HttpStatus.BAD_REQUEST);
    }
  }

  async updateImageOnly(shop_id: string, itemId: number, imageIds: Array<string>) {
    const timestamp = shopeeTimestamp();
    const path = "/api/v2/product/update_item";
    const accessToken = await this.shopeeAuthService.getAccessToken(shop_id);

    const sign = shopeeGenSign(this.partnerKey, this.partnerId + path + timestamp + accessToken + shop_id);
    const url = this.baseUrl + path;
    try {
      const res = await this.httpService.axiosRef.post(
        url,
        {
          item_id: itemId,
          image: { image_id_list: imageIds },
        },
        {
          params: {
            access_token: accessToken,
            partner_id: this.partnerId,
            timestamp,
            sign,
            shop_id,
          },
        },
      );

      if (res.data?.response?.item_id) return res.data.response;

      throw new HttpException("Error while update product", HttpStatus.BAD_REQUEST);
    } catch (err) {
      console.log(err);
      throw new HttpException("Error while update product", HttpStatus.BAD_REQUEST);
    }
  }
}
