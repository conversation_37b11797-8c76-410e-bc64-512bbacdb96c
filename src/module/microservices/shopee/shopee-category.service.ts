import { ShopeeApiAuthService } from "./shopee-auth.service";
import { ConfigService } from "@nestjs/config";
import { HttpService } from "@nestjs/axios";
import { shopeeGenSign, shopeeTimestamp } from "../../../utils/function.util";
import { HttpException, HttpStatus, Injectable } from "@nestjs/common";

@Injectable()
export class ShopeeApiCategoryService {
  constructor(
    private readonly shopeeAuthService: ShopeeApiAuthService,
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
  ) {}

  private readonly baseUrl = this.configService.get<string>("SHOPEE_URL");
  private readonly partnerId = +this.configService.get<string>("SHOPEE_PARTNER_ID");
  private readonly partnerKey = this.configService.get<string>("SHOPEE_PARTNER_KEY");

  async getList(shop_id: string) {
    const timestamp = shopeeTimestamp();
    const path = "/api/v2/product/get_category";
    const accessToken = await this.shopeeAuthService.getAccessToken(shop_id);

    const sign = shopeeGenSign(this.partnerKey, this.partnerId + path + timestamp + accessToken + shop_id);
    const url = this.baseUrl + path;
    try {
      const res = await this.httpService.axiosRef.get(url, {
        params: { access_token: accessToken, partner_id: this.partnerId, timestamp, sign, shop_id },
      });

      if (res.data?.response?.category_list) return res.data.response.category_list;

      throw new HttpException("Error while get category list", HttpStatus.BAD_REQUEST);
    } catch (err) {
      console.log(err);
      throw new HttpException("Error while get category list", HttpStatus.BAD_REQUEST);
    }
  }
}
