import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { IShopeeStoreUpdate } from "./shopee.interface";
import { shopeeGenSign, shopeeTimestamp } from "../../../utils/function.util";
import { ShopeeApiAuthService } from "./shopee-auth.service";
import { ConfigService } from "@nestjs/config";
import { HttpService } from "@nestjs/axios";

@Injectable()
export class ShopeeApiStoreService {
  constructor(
    private readonly shopeeAuthService: ShopeeApiAuthService,
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
  ) {}

  private readonly baseUrl = this.configService.get<string>("SHOPEE_URL");
  private readonly partnerId = +this.configService.get<string>("SHOPEE_PARTNER_ID");
  private readonly partnerKey = this.configService.get<string>("SHOPEE_PARTNER_KEY");

  async getDetail(shop_id: string, accessToken?: string) {
    const timestamp = shopeeTimestamp();
    const path = "/api/v2/shop/get_profile";

    if (!accessToken) {
      accessToken = await this.shopeeAuthService.getAccessToken(shop_id);
    }

    const sign = shopeeGenSign(this.partnerKey, this.partnerId + path + timestamp + accessToken + shop_id);
    const url = this.baseUrl + path;
    try {
      const res = await this.httpService.axiosRef.get(url, {
        headers: { "Content-Type": "application/json" },
        params: { access_token: accessToken, partner_id: this.partnerId, timestamp, sign, shop_id },
      });

      if (res.data?.response) return res.data.response;

      throw new HttpException("Error while get store detail", HttpStatus.BAD_REQUEST);
    } catch (err) {
      console.log(err);
      throw new HttpException("Error while get store detail", HttpStatus.BAD_REQUEST);
    }
  }

  async update(shop_id: string, data: IShopeeStoreUpdate) {
    const timestamp = shopeeTimestamp();
    const path = "/api/v2/shop/update_profile";
    const accessToken = await this.shopeeAuthService.getAccessToken(shop_id);

    const sign = shopeeGenSign(this.partnerKey, this.partnerId + path + timestamp + accessToken + shop_id);
    const url = this.baseUrl + path;
    try {
      const res = await this.httpService.axiosRef.post(url, data, {
        headers: { "Content-Type": "application/json" },
        params: { access_token: accessToken, partner_id: this.partnerId, timestamp, sign, shop_id },
      });

      if (res.data?.response) return res.data.response;

      throw new HttpException("Error while get store detail", HttpStatus.BAD_REQUEST);
    } catch (err) {
      console.log(err);
      throw new HttpException("Error while get store detail", HttpStatus.BAD_REQUEST);
    }
  }
}
