import { Module } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { HttpModule } from "@nestjs/axios";
import { ApiUtilsModule } from "../utils/api-utils.module";
import { MongooseModule } from "@nestjs/mongoose";
import { MarketplaceAuth, MarketplaceAuthSchema } from "../../marketplace/schema/auth.schema";
import { ShopeeApiUtilService } from "./shopee-util.service";
import { ShopeeApiStoreService } from "./shopee-store.service";
import { ShopeeApiAuthService } from "./shopee-auth.service";
import { ShopeeApiProductService } from "./shopee-product.service";
import { ShopeeApiCategoryService } from "./shopee-category.service";
import { ShopeeApiLogisticService } from "./shopee-logistic.service";

@Module({
  imports: [
    HttpModule,
    ApiUtilsModule,
    MongooseModule.forFeature([
      {
        name: MarketplaceAuth.name,
        schema: MarketplaceAuthSchema,
      },
    ]),
  ],
  controllers: [],
  providers: [
    ConfigService,
    ShopeeApiUtilService,
    ShopeeApiStoreService,
    ShopeeApiAuthService,
    ShopeeApiProductService,
    ShopeeApiCategoryService,
    ShopeeApiLogisticService,
  ],
  exports: [
    ShopeeApiUtilService,
    ShopeeApiStoreService,
    ShopeeApiAuthService,
    ShopeeApiProductService,
    ShopeeApiCategoryService,
    ShopeeApiLogisticService,
  ],
})
export class ShopeeApiModule {}
