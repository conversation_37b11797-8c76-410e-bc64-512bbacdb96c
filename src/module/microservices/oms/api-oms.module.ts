/*
https://docs.nestjs.com/modules
*/

import { HttpModule } from "@nestjs/axios";
import { Module } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { ApiOmsService } from "./api-oms.service";
import { ApmModule } from "modules/nestjs-elastic-apm";

@Module({
  imports: [HttpModule, ConfigModule, ApmModule.register()],
  controllers: [],
  providers: [ApiOmsService],
  exports: [ApiOmsService],
})
export class ApiOmsModule {}
