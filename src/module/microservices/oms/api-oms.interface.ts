import { PaymentStatus } from "../../../enum/forstok-salesorder-status.enum";

export interface ISkus {
  quantity?: number;
  name: string;
  sku: string;
  qty: number;
  price: number;
  sale_price: number;
  total_price: number;
  voucher_amount: number;
  promo_amount: number;
  voucher_code: string;
  voucher_seller: number;
  tax_price: number;
  ordered_at: string;
  article_su_code?: string;
}

export interface ICreateCart {
  skus: Array<ISkus>;
  customer_name: string;
  customer_phone: string;
  total_price: number;
  shipping_price: number;
  tax_price: number;
  forstok_id?: string;
  shipping_courier: Record<string, any>;
  store_name: string;
  store_id?: number;
  shipping_address: Record<string, any>;
  address: Record<string, any>;
  marketplace_no: string;
  marketplace_name: string;
  channel: string;
  updated_at?: string;
  payment_method: string;
  forstok_order_id?: number;
  integration_date: string;
  payment_status: PaymentStatus;
  sync_rts: boolean;
  paid_at?: string;
  marketplace_aggregator_id?: string;
  maId?: string;
}

export interface IUpdateOrder {
  orderNumber: string;
  status?: string;
  sku?: string;
  forstok_shipping_courier?: Record<string, any>;
  updatedAt: string;
  shippingPrice?: number;
  paidAt?: string;
}
