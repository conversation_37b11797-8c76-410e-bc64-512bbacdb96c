/*
https://docs.nestjs.com/providers#services
*/

import { HttpService } from "@nestjs/axios";
import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { lastValueFrom, map } from "rxjs";
import { OMSPaymentStatus, PaymentStatus } from "../../../enum/forstok-salesorder-status.enum";
import { ICreateCart, IUpdateOrder } from "./api-oms.interface";

@Injectable()
export class ApiOmsService {
  constructor(private readonly httpService: HttpService, private configService: ConfigService) {}

  private readonly _host = this.configService.get<string>("OMS_HOST");
  private readonly _channel = "marketplace";

  async createOrder(body: ICreateCart) {
    try {
      const mock: Record<string, any> = { payment_status: OMSPaymentStatus.Pending };
      switch (body.payment_status) {
        case PaymentStatus["Payment Verified"]:
          mock.payment_status = OMSPaymentStatus.Paid;
          mock.paid_at = body.paid_at;
          break;
        case PaymentStatus.Cancelled:
          mock.payment_status = OMSPaymentStatus.Cancelled;
          break;
      }
      const cart = await this._createCart(body);

      const requestUrl = this._host + "/api/v1/order/marketplace";
      const requestConfig: any = {
        headers: { channel: this._channel },
      };
      const responseData = await lastValueFrom(
        this.httpService.post(requestUrl, { ...mock, cart: cart.data._id }, requestConfig).pipe(
          map((response) => {
            return response.data;
          }),
        ),
      );
      return responseData.data;
    } catch (e) {
      console.log(e);
      throw new HttpException(
        e.response?.data?.message || e.message,
        e.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async createOrderMarketplace(body: ICreateCart) {
    try {
      const mock: Record<string, any> = { payment_status: OMSPaymentStatus.Pending };
      switch (body.payment_status) {
        case PaymentStatus["Payment Verified"]:
          mock.payment_status = OMSPaymentStatus.Paid;
          mock.paid_at = body.paid_at;
          break;
        case PaymentStatus.Cancelled:
          mock.payment_status = OMSPaymentStatus.Cancelled;
          break;
      }
      const cart = await this._createCartMarketplace(body);

      const requestUrl = this._host + "/api/v1/order/marketplace";
      const requestConfig: any = {
        headers: { channel: this._channel },
      };
      const responseData = await lastValueFrom(
        this.httpService.post(requestUrl, { ...mock, cart: cart.data._id }, requestConfig).pipe(
          map((response) => {
            return response.data;
          }),
        ),
      );
      return responseData.data;
    } catch (e) {
      console.log(e);
      throw new HttpException(
        e.response?.data?.message || e.message,
        e.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async updateOrder(body: IUpdateOrder) {
    try {
      body.forstok_shipping_courier = { ...body.forstok_shipping_courier, shippingPrice: body.shippingPrice };

      const requestUrl = this._host + "/api/v1/order/marketplace/" + body.orderNumber;
      const requestConfig: any = {
        headers: { channel: this._channel },
      };
      const responseData = await lastValueFrom(
        this.httpService
          .patch(
            requestUrl,
            {
              orderStatus: body.status,
              sku: body.sku,
              forstok_shipping_courier: body.forstok_shipping_courier,
              paidAt: body.paidAt,
            },
            requestConfig,
          )
          .pipe(
            map((response) => {
              return response.data;
            }),
          ),
      );
      return responseData.data;
    } catch (err) {
      console.log(err.response);
      throw new HttpException(
        err.response?.data?.message || err,
        err?.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async bulkUpdateOrder(body: Array<IUpdateOrder>) {
    try {
      const requestUrl = this._host + "/api/v1/order/marketplace/bulk-update";
      const requestConfig: any = {
        headers: { channel: this._channel },
      };
      const responseData = await lastValueFrom(
        this.httpService.patch(requestUrl, { data: body }, requestConfig).pipe(
          map((response) => {
            return response.data;
          }),
        ),
      );
      return responseData.data;
    } catch (err) {
      console.log(err.response);
      throw new HttpException(
        err.response?.data?.message || err,
        err?.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getProductDetails(skus: Array<string>) {
    try {
      const requestUrl = this._host + "/api/v1/admin/product/db";
      const requestConfig: any = {
        headers: { channel: this._channel },
        params: { skus },
      };
      const responseData = await lastValueFrom(
        this.httpService.get(requestUrl, requestConfig).pipe(
          map((response) => {
            return response.data;
          }),
        ),
      );
      return responseData.data;
    } catch (err) {
      console.log(err.response);
      throw new HttpException(
        err.response?.data?.message || err,
        err?.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getProductMpList() {
    try {
      const requestUrl = this._host + "/api/v1/product-mp-id";
      const responseData = await lastValueFrom(
        this.httpService.get(requestUrl).pipe(
          map((response) => {
            return response.data;
          }),
        ),
      );
      return responseData.data;
    } catch (err) {
      console.log(err.response);
      throw new HttpException(
        err.response?.data?.message || err,
        err?.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  private async _createCartMarketplace(params: ICreateCart) {
    try {
      const requestUrl = this._host + "/api/v1/cart/marketplace-aggregator";
      const requestConfig: any = {
        headers: { channel: this._channel },
      };

      return await lastValueFrom(
        this.httpService.post(requestUrl, params, requestConfig).pipe(
          map((response) => {
            return response.data;
          }),
        ),
      );
    } catch (e) {
      console.log(e.response);
      throw new HttpException(e.response?.data?.message || e, e?.response?.status || HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  private async _createCart(params: ICreateCart) {
    try {
      const requestUrl = this._host + "/api/v1/cart/marketplace";
      const requestConfig: any = {
        headers: { channel: this._channel },
      };

      return await lastValueFrom(
        this.httpService.post(requestUrl, params, requestConfig).pipe(
          map((response) => {
            return response.data;
          }),
        ),
      );
    } catch (e) {
      console.log(e.response);
      throw new HttpException(e.response?.data?.message || e, e?.response?.status || HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
