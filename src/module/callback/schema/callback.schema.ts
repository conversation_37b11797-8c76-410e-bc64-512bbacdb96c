import { <PERSON>p, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Document, SchemaTypes } from "mongoose";

@Schema({ timestamps: true })
export class Callback {
  @Prop()
  event: string;

  @Prop()
  profile_id: number;

  @Prop({ type: SchemaTypes.Mixed })
  order: Record<string, any>;

  @Prop()
  payload: string;

  @Prop()
  status: boolean;

  @Prop()
  error_message: string;
}

export type CallbackDocument = Callback & Document;

export const CallbackSchema = SchemaFactory.createForClass(Callback);
