import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Document, SchemaTypes } from "mongoose";

@Schema({ timestamps: true })
export class CallbackMarketplaceAggregator {
  @Prop()
  maId: String;

  @Prop({ type: SchemaTypes.Mixed })
  cancellation: Record<string, any>;

  @Prop()
  companyId: string;

  @Prop()
  currency: string;

  @Prop()
  createdAt: Date;

  @Prop({ type: [SchemaTypes.Mixed] })
  items: Record<string, any>[];

  @Prop({ type: SchemaTypes.Mixed })
  payment: Record<string, any>;

  @Prop({ type: SchemaTypes.Mixed })
  shipment: Record<string, any>;

  @Prop({ type: SchemaTypes.Mixed })
  timeline: Record<string, any>;

  @Prop()
  marketplaceCode: string;

  @Prop()
  noteUpdateTime: string;

  @Prop()
  orderId: string;

  @Prop()
  orderStatus: string;

  @Prop()
  splitUp: string;

  @Prop()
  updatedAt: Date;
}

export type CallbackMarketplaceAggregatorDocument = CallbackMarketplaceAggregator & Document;

export const CallbackMarketplaceAggregatorSchema = SchemaFactory.createForClass(CallbackMarketplaceAggregator);
