import { Body, Controller, Get, HttpCode, HttpException, HttpStatus, Post, Query, Req } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { ApiTags } from "@nestjs/swagger";
import { InternalAccess, InternalAccessMethod, Public } from "keycloak-connect-tbs";
import { ForstokService } from "../forstok/forstok.service";
import { CallbackV1Service } from "./callback-v1.service";
import { MigrateCallbackDto } from "./dto/migrate-callback.dto";
import { RegisterCallbackDto } from "./dto/register-callback.dto";

@ApiTags("Callbacks")
@Controller("callback-v1")
export class CallbackV1Controller {
  constructor(
    private readonly callbackService: CallbackV1Service,
    private readonly forstokService: ForstokService,
    private readonly configService: ConfigService,
  ) {}

  @ApiTags("ip-whitelist:public")
  @Post("create")
  @HttpCode(HttpStatus.OK)
  @Public()
  async create(@Body() body, @Req() req) {
    this._checkRegisterKey(req.headers["x-api-key"]);

    return this.callbackService.create(body);
  }

  @ApiTags("ip-whitelist:public")
  @Post("update")
  @HttpCode(HttpStatus.OK)
  @Public()
  async update(@Body() body, @Req() req: Record<string, any>) {
    this._checkRegisterKey(req.headers["x-api-key"]);
    return this.callbackService.create(body);
  }

  @Post("register-callback")
  async register(@Body() body: RegisterCallbackDto) {
    return this.forstokService.registerCallback(body.event, body.url);
  }

  @Post("force-push-order")
  @InternalAccess(InternalAccessMethod.STRICT)
  async forcePushOrder(@Body() body: Record<string, any>) {
    return this.callbackService.forcePushOrder(body);
  }

  @Get()
  async findAll() {
    return this.forstokService.getCallback();
  }

  @Get("migrate-callback")
  async migrateCallback(@Query() query: MigrateCallbackDto) {
    return this.callbackService.migrateCallback(query);
  }

  @Get("sync-logs")
  @InternalAccess(InternalAccessMethod.STRICT)
  async syncLogs() {
    return this.callbackService.syncLogs();
  }

  @Get("sync-open-order")
  @InternalAccess(InternalAccessMethod.STRICT)
  async syncOpenOrder() {
    return this.callbackService.syncOpenOrder();
  }

  @Get("sync-error-order")
  @InternalAccess(InternalAccessMethod.STRICT)
  async syncErrorOrder() {
    return this.callbackService.syncErrorOrder();
  }

  private _checkRegisterKey(key: string) {
    const allowedKey = this.configService.get<string>("MARKETPLACE_AGGREGATOR");

    if (allowedKey !== key) throw new HttpException("Key unregistered", HttpStatus.FORBIDDEN);

    return true;
  }
}
