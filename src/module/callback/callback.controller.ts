import { CallbackService } from "./callback.service";
import { Body, Controller, Get, HttpCode, HttpException, HttpStatus, Post, Query, Req } from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import { ForstokService } from "../forstok/forstok.service";
import { RegisterCallbackDto } from "./dto/register-callback.dto";
import { MigrateCallbackDto } from "./dto/migrate-callback.dto";
import { InternalAccess, InternalAccessMethod, Public } from "keycloak-connect-tbs";
import { ConfigService } from "@nestjs/config";

@ApiTags("Callbacks")
@Controller("callback")
export class CallbackController {
  constructor(
    private readonly callbackService: CallbackService,
    private readonly forstokService: ForstokService,
    private readonly configService: ConfigService,
  ) {}

  @ApiTags("ip-whitelist:public")
  @Post("create")
  @HttpCode(HttpStatus.OK)
  @Public()
  async create(@Body() body, @Req() req: Record<string, any>) {
    this._checkRegisterKey(req.headers.key);
    return this.callbackService.create(body);
  }

  @ApiTags("ip-whitelist:public")
  @Post("update")
  @HttpCode(HttpStatus.OK)
  @Public()
  async update(@Body() body, @Req() req: Record<string, any>) {
    this._checkRegisterKey(req.headers.key);
    return this.callbackService.create(body);
  }

  @Post("register-callback")
  async register(@Body() body: RegisterCallbackDto) {
    return this.forstokService.registerCallback(body.event, body.url);
  }

  @Post("force-push-order")
  @InternalAccess(InternalAccessMethod.STRICT)
  async forcePushOrder(@Body() body: Record<string, any>) {
    return this.callbackService.forcePushOrder(body);
  }

  @Get()
  async findAll() {
    return this.forstokService.getCallback();
  }

  @Get("migrate-callback")
  async migrateCallback(@Query() query: MigrateCallbackDto) {
    return this.callbackService.migrateCallback(query);
  }

  @Get("sync-logs")
  @InternalAccess(InternalAccessMethod.STRICT)
  async syncLogs() {
    return this.callbackService.syncLogs();
  }

  @Get("sync-open-order")
  @InternalAccess(InternalAccessMethod.STRICT)
  async syncOpenOrder() {
    return this.callbackService.syncOpenOrder();
  }

  @Get("sync-error-order")
  @InternalAccess(InternalAccessMethod.STRICT)
  async syncErrorOrder() {
    return this.callbackService.syncErrorOrder();
  }

  private _checkRegisterKey(key: string) {
    const allowedKey = this.configService.get<string>("FORSTOK_REGISTER_KEY");

    if (allowedKey !== key) throw new HttpException("Key unregistered", HttpStatus.FORBIDDEN);

    return true;
  }
}
