import { Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { InjectModel } from "@nestjs/mongoose";
import * as dayjs from "dayjs";
import { Model, PaginateModel } from "mongoose";
import {
  ForstokOrderStatusPriority,
  ForstokStatus,
  ForstokStatusPriority,
} from "../../enum/forstok-salesorder-status.enum";
import { keysToSnackCase } from "../../utils/function.util";
import { ForstokService } from "../forstok/forstok.service";
import { GoldService } from "../gold/gold.service";
import { ForstokOrderError, ForstokOrderErrorDocument } from "../marketplace/schema/forstok-order-error.schema";
import { ForstokOrder, ForstokOrderDocument } from "../marketplace/schema/forstok-order.schema";
import { ForstokTempStock, ForstokTempStockDocument } from "../marketplace/schema/forstok-temp-stock";
import { ApiOmsService } from "../microservices/oms/api-oms.service";
import { ApiUtilsService } from "../microservices/utils/api-utils.service";
import { MigrateCallbackDto } from "./dto/migrate-callback.dto";
import { Callback, CallbackDocument } from "./schema/callback.schema";
import { CallbackQueueWorkerService } from "./callback-queue-worker.service";

@Injectable()
export class CallbackService {
  constructor(
    @InjectModel(Callback.name)
    private readonly callback: Model<CallbackDocument>,
    @InjectModel(ForstokOrder.name)
    private forstokOrderModel: PaginateModel<ForstokOrderDocument>,
    @InjectModel(ForstokOrderError.name)
    private forstokOrderErrorModel: PaginateModel<ForstokOrderErrorDocument>,
    @InjectModel(ForstokTempStock.name)
    private forstokTempStockModel: Model<ForstokTempStockDocument>,
    private readonly configService: ConfigService,
    private readonly forstokService: ForstokService,
    private readonly omsService: ApiOmsService,
    private readonly utilService: ApiUtilsService,
    private readonly goldService: GoldService,
    private readonly queueWorker: CallbackQueueWorkerService,
  ) {}

  private processedChannelList = [];

  // async test() {
  //   await Promise.all([
  //     this.utilService.test(1, "Open"),
  //     this.utilService.test(2, "Open"),
  //     this.utilService.test(3, "Ready to Pick"),
  //     this.utilService.test(4, "Ready to Ship"),
  //     this.utilService.test(5, "Completed"),
  //     this.utilService.test(6, "Shipped"),
  //   ]);
  //   return true;
  // }

  async create(body: Record<string, any>) {
    await this._getProcessedChannelList();

    if (new Date(body.order.ordered_at) < new Date("2023-09-05T00:00:00.000Z")) return;

    if (this.processedChannelList.includes(Number(body.order.channel_id))) {
      const insert = await this.callback.create(body);
      await this.queueWorker.addToQueue(body.order.id, body.order.status, insert._id);
    }

    return "successfully process order";
  }

  async migrateCallback(params: MigrateCallbackDto) {
    // const gracePeriodeHash = {};

    const [data] = await Promise.all([
      this.forstokService.getCallbackHistory(params.from, params.to),
      // this.utilService.getConfigByPrefix("mp.grace_period"),
      this._getProcessedChannelList(),
    ]);

    // gracePeriode?.docs?.map((item) => {
    //   const gracePeriodUnit = (item?.value || "0 hour").split(" ");
    //   gracePeriodeHash[item.key] = dayjs(new Date()).subtract(gracePeriodUnit[0], gracePeriodUnit[1]).toDate();
    // });

    const limit = 500;
    const batch = Math.ceil(data.total_data / limit);

    for (let i = 0; i < batch; i++) {
      const batchData = data.response.splice(0, limit);
      await Promise.all(
        batchData.map(async (item) => {
          // const gracePeriod = gracePeriodeHash[`mp.grace_period.${item.channelId}`];

          const payload = JSON.parse(item.payload);
          const order = payload.order;
          const insert = await this.callback.create({
            event: item.event,
            order,
            profile_id: item.profile_id,
            createdAt: item.createdAt,
            updatedAt: item.updatedAt,
          });

          await this.queueWorker.addToQueue(order.id, order.status, insert._id);
          // await this._queueSession(order.id);
          // await this.queueWorker.processData(order, gracePeriod, this.processedChannelList);
          // await this._endSession(order.id);
        }),
      );
    }
  }

  async syncLogs() {
    console.log("==== START SYNC CALLBACK ====");
    console.time("Total Time");
    const lastFetch = await this.utilService.getConfig(`mp.last_fetch_webhook_logs`, false);
    const fetchDate = dayjs(lastFetch.value).format("YYYY-MM-DD");
    const now = dayjs();
    console.time("Fetch Data Time");
    const logs = await this.forstokService.getCallbackHistory(fetchDate, now.format("YYYY-MM-DD"));
    console.timeEnd("Fetch Data Time");

    const limitPerBatch = 300;
    const dataLength = logs.total_data;
    const totalBatch = Math.ceil(dataLength / limitPerBatch);
    let startIndex = 0;

    let endIndex = limitPerBatch;
    let errorCount = 0;
    let successCount = 0;
    let i = -1;
    while (++i < totalBatch) {
      console.time(`Total Time Batch ${i + 1} / ${totalBatch}`);
      const data = {};
      const ids = {};
      let dataCount = 0;

      try {
        for (let j = startIndex; j < endIndex; j++) {
          if (new RegExp(this.configService.get<string>("BASE_URL")).test(logs.response[j].url)) {
            const payload = JSON.parse(logs.response[j].payload);

            const mock = { ...payload, createdAt: logs.response[j].created_at, updatedAt: logs.response[j].updated_at };
            if (!data[payload.order.id]) {
              data[payload.order.id] = [mock];
            } else {
              data[payload.order.id].push(mock);
            }
            ids[payload.order.id] = true;
            dataCount++;
          }
        }

        const orders = await this.forstokOrderModel.find({ forstokOrderId: { $in: Object.keys(ids) } }).lean();
        const hashOrder = {};
        orders.map((order) => (hashOrder[order.forstokOrderId] = order));

        await Promise.all(
          Object.keys(data).map(async (key) => {
            for (const item of data[key]) {
              const order = hashOrder[item.order.id];
              successCount += await this._processLogs(order, item);
            }
          }),
        );

        startIndex = endIndex;
        endIndex = endIndex + limitPerBatch < dataLength - 1 ? endIndex + limitPerBatch : dataLength - 1;
        successCount += dataCount;
      } catch (err) {
        errorCount += dataCount;
      }
      console.timeEnd(`Total Time Batch ${i + 1} / ${totalBatch}`);
    }

    await this.utilService.updateConfig(lastFetch.key, now.toISOString());
    console.timeEnd("Total Time");

    console.log(`Success ${successCount} data`);
    if (errorCount) console.log(`There's error for ${errorCount} data`);

    console.log("==== END SYNC CALLBACK ====");
  }

  async forcePushOrder(payload: Record<string, any>) {
    const formattedMock = await keysToSnackCase(payload);
    return await this.queueWorker.processData({ ...formattedMock, force_push: true }, null);
  }

  async syncOpenOrder() {
    console.log("==== START SYNC OPEN ORDER ====");
    // const gracePeriodeHash = {};

    await this._getProcessedChannelList();

    const data = await this.forstokOrderModel
      .find({
        status: [ForstokStatus.Open, ForstokStatus.Printed, ForstokStatus.ReadyToShip, ForstokStatus.NotShipped],
        orderNumber: { $exists: false },
        channelId: this.processedChannelList,
      })
      .lean();
    // const [data, gracePeriode] = await Promise.all([
    //   this.forstokOrderModel
    //     .find({
    //       status: [ForstokStatus.Open, ForstokStatus.Printed, ForstokStatus.ReadyToShip, ForstokStatus.NotShipped],
    //       orderNumber: { $exists: false },
    //       channelId: this.processedChannelList,
    //     })
    //     .lean(),
    //   this.utilService.getConfigByPrefix("mp.grace_period"),
    // ]);

    // gracePeriode?.docs?.map((item) => {
    //   const gracePeriodUnit = (item?.value || "0 hour").split(" ");
    //   gracePeriodeHash[item.key] = dayjs(new Date()).subtract(gracePeriodUnit[0], gracePeriodUnit[1]).toDate();
    // });

    const limit = 50;
    const dataLength = data.length;
    const totalBatch = Math.ceil(dataLength / limit);
    let startIndex = 0;
    let endIndex = Math.min(dataLength, limit);
    let i = -1;
    while (++i < totalBatch) {
      console.time(`Total Time Sync Order Batch ${i + 1} / ${totalBatch}`);
      for (let j = startIndex; j < endIndex; j++) {
        let item = data[j];
        // const gracePeriod = gracePeriodeHash[`mp.grace_period.${item.channelId}`];
        item = await keysToSnackCase(item);
        // await this._queueSession(item["forstok_order_id"]);
        await this.queueWorker.processData({ ...item, id: item["forstok_order_id"] }, null);
        // await this._processData({ ...item, id: item["forstok_order_id"] }, gracePeriod);
        // await this._endSession(item["forstok_order_id"]);
      }

      startIndex = endIndex;
      endIndex = endIndex + limit < dataLength - 1 ? endIndex + limit : dataLength - 1;
      console.timeEnd(`Total Time Sync Order Batch ${i + 1} / ${totalBatch}`);
    }
    console.log("==== END SYNC OPEN ORDER ====");
  }
  async syncErrorOrder() {
    const count = await this.forstokOrderErrorModel.count();
    const limit = 250;
    const totalBatch = Math.ceil(count / limit);

    // const [gracePeriode] = await Promise.all([
    //   this.utilService.getConfigByPrefix("mp.grace_period"),
    //   this._getProcessedChannelList(),
    // ]);

    await this._getProcessedChannelList();

    // const gracePeriodeHash = {};
    // gracePeriode?.docs?.map((item) => {
    //   const gracePeriodUnit = (item?.value || "0 hour").split(" ");
    //   gracePeriodeHash[item.key] = dayjs(new Date()).subtract(gracePeriodUnit[0], gracePeriodUnit[1]).toDate();
    // });

    console.time("Total Time");
    console.log("==== START SYNC ERROR ORDER ====");

    let i = -1;
    while (++i < totalBatch) {
      console.time(`Total Time Sync Batch ${i + 1} / ${totalBatch}`);

      const data = await this.forstokOrderErrorModel.find({}, {}, { skip: limit * i, sort: "_id", limit, lean: true });

      for (const item of data) {
        // const gracePeriod = gracePeriodeHash[`mp.grace_period.${item.channelId}`];
        // await this._queueSession(item.forstokOrderId);
        await this.queueWorker.processData({ ...(await keysToSnackCase(item)), id: item.forstokOrderId }, null);
        // await this._endSession(item.forstokOrderId);
      }

      console.timeEnd(`Total Time Sync Batch ${i + 1} / ${totalBatch}`);
    }

    if (totalBatch) console.log("\n");
    console.timeEnd("Total Time");
    console.log("==== END SYNC ERROR ORDER ====");
  }

  private async _getProcessedChannelList() {
    const data = await this.utilService.getConfig("mp.process_channel");

    if (data) {
      this.processedChannelList = data.split(",").map((item) => Number(item));
    }
  }

  private async _processLogs(order: Record<string, any>, log: Record<string, any>) {
    try {
      const existCallback = await this.callback.findOne({ payload: JSON.stringify(log) }).lean();

      const statusCallback = ForstokStatusPriority[log.order.status === "Completed" ? "Complete" : log.order.status];
      const statusOrder = ForstokOrderStatusPriority[order?.status === "Completed" ? "Complete" : order?.status];

      if (statusCallback <= statusOrder) return 0;
      if (existCallback && existCallback.status) return 0;
      if (existCallback) await this.callback.findByIdAndDelete(existCallback.id);

      log.dontCreateCart = false;
      await this.create(log);

      return 1;
    } catch (err) {
      console.log(err);
      return 0;
    }
  }
}
