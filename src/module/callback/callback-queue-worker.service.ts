import { DEFAULT_REDIS_NAMESPACE, InjectRedis } from "@liaoliaots/nestjs-redis";
import { ConfigService } from "@nestjs/config";
import { InjectModel } from "@nestjs/mongoose";
import { Interval } from "@nestjs/schedule";
import { isBase64 } from "class-validator";
import * as dayjs from "dayjs";
import Redis from "ioredis";
import { Model, PaginateModel } from "mongoose";
import { RedisNsAutoRts } from "../../enum/callback.enum";
import {
  ForstokChannel,
  ForstokStatus,
  ForstokStatusPriority,
  OMSOrderStatus,
  PaymentStatus,
} from "../../enum/forstok-salesorder-status.enum";
import { stringToArray } from "../../utils/function.util";
import { ForstokService } from "../forstok/forstok.service";
import { ForstokOrderError, ForstokOrderErrorDocument } from "../marketplace/schema/forstok-order-error.schema";
import { ForstokOrder, ForstokOrderDocument } from "../marketplace/schema/forstok-order.schema";
import { ForstokTempStock, ForstokTempStockDocument } from "../marketplace/schema/forstok-temp-stock";
import { ISkus } from "../microservices/oms/api-oms.interface";
import { ApiOmsService } from "../microservices/oms/api-oms.service";
import { ApiUtilsService } from "../microservices/utils/api-utils.service";
import { Callback, CallbackDocument } from "./schema/callback.schema";

export class CallbackQueueWorkerService {
  private thisPodsProcessQueue = false;
  private processedChannelList = [];
  private autoRtsChannelList = [];
  private readonly redisRtsKey = "rts-";
  private readonly redisConfirmKey = "confirm-";
  private readonly availableListKey = "order-available-queue";
  private readonly processedListKey = "order-processed-queue";
  private readonly lockedListKey = "order-locked-queue";
  private readonly waitingListKey = "order-waiting-queue";
  private readonly autoReleaseKey = "order-auto-release-queue";

  constructor(
    @InjectModel(Callback.name)
    private readonly callback: Model<CallbackDocument>,
    @InjectModel(ForstokOrder.name)
    private forstokOrderModel: PaginateModel<ForstokOrderDocument>,
    @InjectModel(ForstokOrderError.name)
    private forstokOrderErrorModel: PaginateModel<ForstokOrderErrorDocument>,
    @InjectModel(ForstokTempStock.name)
    private forstokTempStockModel: Model<ForstokTempStockDocument>,
    @InjectRedis(DEFAULT_REDIS_NAMESPACE)
    private readonly redisOrderService: Redis,
    @InjectRedis(RedisNsAutoRts)
    private readonly redisRtsService: Redis,
    private readonly configService: ConfigService,
    private readonly forstokService: ForstokService,
    private readonly omsService: ApiOmsService,
    private readonly utilService: ApiUtilsService,
  ) {}

  async addToQueue(id: number, status: string, callback_id: string) {
    const script = `
      local availList = KEYS[1]
      local workedList = KEYS[2]
      local waitingList = KEYS[3]

      local forstokId = ARGV[1]
      local callbackId = ARGV[2]

      local isWaiting = false
      local keyPrefix = 'orders:'

      -- check when forstokId already processed on queue then must be waiting until it finish before can be processed again
      if redis.call('LPOS', workedList, forstokId) then
        if not redis.call('LPOS', waitingList, forstokId) then
          redis.call('RPUSH', waitingList, forstokId)
        end
        keyPrefix = keyPrefix .. 'waiting-'
        isWaiting = true
      end

      -- check when forstokId doesn't exists on available list then push it
      if isWaiting == false and not redis.call('LPOS', availList, forstokId) then
        redis.call('RPUSH', availList, forstokId)
      end

      -- push callback_id to order key list
      return redis.call('RPUSH', keyPrefix .. forstokId, callbackId)
    `;

    return this.redisOrderService.eval(
      script,
      3,
      this.availableListKey,
      this.processedListKey,
      this.waitingListKey,
      id,
      callback_id,
    );
  }

  @Interval(20)
  async runQueue() {
    if (this.thisPodsProcessQueue) return;

    this.thisPodsProcessQueue = true;
    await this._autoReleaseLock();

    const timestamp = Date.now();
    const script = `
      local availListKey = KEYS[1]
      local workedListKey = KEYS[2]
      local lockListKey = KEYS[3]

      local timestamp = ARGV[1]

      local idToProceed = {}

      for i=0, 10 do
        local id = redis.call('LPOP', availListKey)

        if not id then
          break
        end

        local lockVal = {}
        lockVal['id'] = id
        lockVal['timestamp'] = timestamp

        redis.call('RPUSH', lockListKey, cjson.encode(lockVal))
        redis.call('RPUSH', workedListKey, id)

        table.insert(idToProceed, id)
      end

      return idToProceed
    `;

    const forstokIds = (await this.redisOrderService.eval(
      script,
      3,
      this.availableListKey,
      this.processedListKey,
      this.lockedListKey,
      timestamp,
    )) as Array<string>;

    await Promise.all(
      forstokIds?.map(async (forstokId) => {
        let lists = await this.redisOrderService.lrange(`orders:${forstokId}`, 0, -1);
        try {
          const callbacks = await this.callback.find({ _id: lists }).lean();

          if (!callbacks?.length) return;

          let sorted = [];
          let channelId = "";
          callbacks.map((cb) => {
            const { order } = cb;
            const priority = ForstokStatusPriority[order.status !== "Completed" ? order.status : "Complete"];
            sorted.push({ ...cb, order: { ...order, priority } });
            channelId = order.channel_id;
          });

          sorted = sorted.sort(
            (a, b) =>
              a.order.priority - b.order.priority ||
              dayjs(a.order.updated_at).unix() - dayjs(b.order.updated_at).unix(),
          );

          const [defaultCourier] = await Promise.all([
            this.utilService.getConfig(`mp.default_courier.${channelId}`),
            this._getProcessedChannelList(),
            this._getAutoRtsChannelList(),
          ]);

          for (const cb of sorted) {
            try {
              if (defaultCourier) {
                cb.order.shipping_provider = defaultCourier.shippingProvider;
                cb.order.shipping_provider_type = defaultCourier.shippingProviderType;
              }

              await this.processData(cb.order, null);

              cb.status = true;
              lists = lists.filter((a) => a !== cb._id.toString());
            } catch (err) {
              console.dir({
                title: "Error processData",
                err: err.message,
              });
              cb.status = false;
              cb.error_message = err.message;
            } finally {
              await this.callback.updateOne({ _id: cb._id }, cb);
            }
          }
        } catch (err) {
          console.dir({
            title: "Error process queue",
            err,
          });
        } finally {
          await this._clearLock(forstokId, lists, timestamp);
        }
      }),
    );

    this.thisPodsProcessQueue = false;
  }

  private async _clearLock(forstokId: string, lists: Array<string>, timestamp: number) {
    const script = `
      local waitingKey = KEYS[1]
      local availableKey = KEYS[2]
      local lockKey = KEYS[3]
      local processKey = KEYS[4]

      local id = ARGV[1]
      local list = cjson.decode(ARGV[2])
      local lockVal = ARGV[3]

      local inWaiting = redis.call('LREM', waitingKey, 1, id)

      if inWaiting > 0 then
        local listWaiting = redis.call('LRANGE', 'orders:waiting-' .. id, 0, -1)
        redis.call('DEL', 'orders:waiting-' .. id)
        redis.call('RPUSH', availableKey, id)

        if #listWaiting > 0 then
          for _, val in ipairs(listWaiting) do
            table.insert(list, val)
          end
        end
      end

      redis.call('DEL', 'orders:' .. id)

      if #list > 0 then
        for _, val in ipairs(list) do
          redis.call('RPUSH', 'orders:' .. id, val)
        end
      end

      redis.call('LREM', processKey, 1, id)
      redis.call('LREM', lockKey, 1, lockVal)
    `;

    await this.redisOrderService.eval(
      script,
      4,
      this.waitingListKey,
      this.availableListKey,
      this.lockedListKey,
      this.processedListKey,
      forstokId,
      JSON.stringify(lists),
      JSON.stringify({ id: forstokId + "", timestamp: timestamp + "" }),
    );
  }

  private async _autoReleaseLock() {
    const lastRelease = await this.redisOrderService.get(this.autoReleaseKey);

    if (lastRelease && dayjs().diff(dayjs(Number(lastRelease)), "minutes") < 1) {
      return;
    }

    await this.redisOrderService.set(this.autoReleaseKey, Date.now());

    const maxLock = (await this.utilService.getConfig("mp.queue-max-lock")) || "15 minute";
    const [value, unit] = maxLock.split(" ");

    const list = await this.redisOrderService.lrange(this.lockedListKey, 0, -1);

    for (const item of list) {
      const { id, timestamp } = JSON.parse(item);

      if (dayjs(timestamp) < dayjs().subtract(value, unit)) {
        await Promise.all([
          this.redisOrderService.lrem(this.lockedListKey, 1, item),
          this.redisOrderService.lrem(this.processedListKey, 1, id),
        ]);

        const inWaiting = await this.redisOrderService.lrem(this.waitingListKey, 1, id);

        if (inWaiting) {
          const [listCbWaiting, listCb] = await Promise.all([
            this.redisOrderService.lrange(`orders:waiting-${id}`, 0, -1),
            this.redisOrderService.lrange(`orders:${id}`, 0, -1),
            this.redisOrderService.rpush(this.availableListKey, id),
          ]);

          const newList = [...(listCb || []), ...(listCbWaiting || [])];
          if (newList.length) {
            await this.redisOrderService.del(`orders:${id}`);
            await this.redisOrderService.rpush(`orders:${id}`, ...newList);
          }
        }
      }
    }
  }

  async processData(order: Record<string, any>, gracePeriod: Date) {
    if (order.total_price < 1 && !order.force_push) return;

    let mock;
    let lastStatusPriority = ForstokStatusPriority[order.status]; // for save the last status before changed below
    let lastStatus = order.status;
    const existData = await this.forstokOrderModel.findOne({ forstokOrderId: order.id }).lean();
    try {
      if (existData) {
        lastStatus = existData.status;
        lastStatusPriority = ForstokStatusPriority[existData.status];
      }

      mock = await this._prepareToSave(order, existData);

      if (order.status === ForstokStatus.Cancelled) {
        if (existData) {
          await this._handleCancelled(
            existData.orderNumber,
            existData.updatedAt.toISOString(),
            existData.forstokOrderId + "",
          );
          return await this.forstokOrderModel.findByIdAndUpdate(existData._id, { status: ForstokStatus.Cancelled });
        } else {
          return await this.forstokOrderModel.create(mock);
        }
      }

      if (existData?.status === ForstokStatus.Cancelled) return;

      order.shipping_courier.city = order.address.city;
      order.shipping_courier.postalCode = order.address.postal_code;
      const saved = (await this.forstokOrderModel.findOneAndUpdate({ forstokOrderId: order.id }, mock, {
        upsert: true,
        new: true,
        lean: true,
      })) as any;

      if (mock.status === "Completed") mock.status = ForstokStatus.Complete;

      const compareDate = saved.paidAt || saved.orderedAt;

      if (!gracePeriod || new Date(compareDate) <= gracePeriod) {
        switch (order.status) {
          case ForstokStatus.NotPaid:
            await this._processOpenOrder(order, saved, lastStatus, lastStatusPriority);

            break;
          case ForstokStatus.Open:
            await this._processOpenOrder(order, saved, lastStatus, lastStatusPriority);
            break;
          case ForstokStatus.NotShipped:
            await this._processOpenOrder(order, saved, lastStatus, lastStatusPriority);
            break;
          case ForstokStatus.Printed:
            await this._processOpenOrder(order, saved, lastStatus, lastStatusPriority);
            break;
          case ForstokStatus.ReadyToShip:
            await this._processOpenOrder(order, saved, lastStatus, lastStatusPriority);
            break;
          case ForstokStatus.Shipped:
            await this._handleShipped(
              saved.orderNumber,
              saved.updatedAt.toISOString(),
              saved.shippingCourier,
              saved.shippingPrice,
              saved.forstokOrderId + "",
            );
            break;
          case ForstokStatus.Delivered:
            await this._handleDelivered(saved.orderNumber, saved.updatedAt.toISOString(), saved.forstokOrderId + "");
            break;
          case ForstokStatus.Complete:
            await this._handleComplete(saved.orderNumber, saved.updatedAt.toISOString(), saved.forstokOrderId + "");
            break;
          case ForstokStatus.Cancelled:
            await this._handleCancelled(saved.orderNumber, saved.updatedAt.toISOString(), saved.forstokOrderId + "");
            await this.forstokOrderModel.updateOne({ _id: saved._id }, { status: ForstokStatus.Cancelled });
            break;
          default:
            break;
        }
      }

      await this.forstokOrderErrorModel.deleteOne({ forstokOrderId: order.id });
    } catch (err) {
      console.log(err);

      if (existData) await this.forstokOrderModel.updateOne({ _id: existData._id }, existData);
      await this._handleErrorProcess(mock, err?.message || err);
    }
  }

  private async _processOpenOrder(
    order: Record<string, any>,
    saved: ForstokOrderDocument,
    lastStatus: string,
    lastStatusPriority: string,
  ) {
    await this._handleOpen(order, saved);
    const cbStatusPriority = ForstokStatusPriority[order.status];
    let newStatus = saved.status;

    // set back status to last status when current callback status less than last db status priority
    // ex: cb status = Printed (priority 2) db status Ready to Pick (priority 4)
    // then this if will set back status to Ready to Pick
    if (lastStatusPriority > cbStatusPriority) newStatus = lastStatus;
    return this.forstokOrderModel.updateOne({ _id: saved._id }, { status: newStatus });
  }

  private async _handleOpen(order: Record<string, any>, saved: ForstokOrderDocument) {
    try {
      const items = this._mapItemForOms(
        saved.itemLines,
        saved.warehouseId,
        saved.orderedAt.toISOString(),
        saved.channelId,
      );

      if (this.autoRtsChannelList.includes(saved.channelId)) {
        await this._setRts(saved.forstokOrderId, saved.channelId + "");
      }

      let orderCreated;
      if (!saved.orderNumber && this.processedChannelList.includes(String(order.channel_id))) {
        orderCreated = await this._createOrderOms(saved, order, Object.values(items));
      }
      const mock: Record<string, any> = { orderNumber: saved.orderNumber || orderCreated?.orderNumber };

      if (order.payment.status === PaymentStatus["Payment Verified"]) {
        await this.omsService.updateOrder({
          orderNumber: mock.orderNumber,
          updatedAt: saved.updatedAt.toISOString(),
          status: OMSOrderStatus.Paid,
          forstok_shipping_courier: saved.shippingCourier,
          shippingPrice: saved.shippingPrice,
          paidAt: saved.paidAt.toISOString(),
        });
      }

      return await this.forstokOrderModel.updateOne({ _id: saved._id }, mock);
    } catch (err) {
      console.log(err);
      throw err.response?.data?.message || err.message;
    }
  }

  private async _handleShipped(
    orderNumber: string,
    updatedAt: string,
    forstok_shipping_courier: Record<string, any>,
    shippingPrice: number,
    orderId: string,
  ) {
    if (orderNumber) {
      await this.omsService.updateOrder({
        orderNumber,
        updatedAt,
        status: OMSOrderStatus.OnShipment,
        forstok_shipping_courier,
        shippingPrice,
      });
      await this.forstokOrderModel.updateOne({ orderNumber }, { syncRts: true });
      return await this._delRtsFromRedis(orderId);
    }
  }

  private async _handleDelivered(orderNumber: string, updatedAt: string, orderId: string) {
    if (orderNumber) {
      await this.omsService.updateOrder({
        orderNumber,
        updatedAt,
        status: OMSOrderStatus.Delivered,
      });
      return await this._delRtsFromRedis(orderId);
    }
  }

  private async _handleComplete(orderNumber: string, updatedAt: string, orderId: string) {
    if (orderNumber) {
      await this.omsService.updateOrder({
        orderNumber,
        updatedAt,
        status: OMSOrderStatus.Complete,
      });
      return await this._delRtsFromRedis(orderId);
    }
  }

  private async _handleCancelled(orderNumber: string, updatedAt: string, orderId: string) {
    if (orderNumber) {
      await this.omsService.updateOrder({
        orderNumber,
        updatedAt,
        status: OMSOrderStatus.Cancelled,
      });
      return await this._delRtsFromRedis(orderId);
    }
  }

  private async _handleErrorProcess(order: Record<string, any>, err: string) {
    await this.forstokOrderModel.deleteOne({ forstokOrderId: order.forstokOrderId });

    return await this.forstokOrderErrorModel
      .findOneAndUpdate(
        { forstokOrderId: order.forstokOrderId },
        { ...order, errorMessage: err },
        { upsert: true, new: true },
      )
      .exec();
  }

  private _mapItemForOms(
    items: Array<Record<string, any>>,
    warehouse_id: number,
    ordered_at: string,
    channel: ForstokChannel,
  ): Record<string, ISkus> {
    const res: Record<string, ISkus> = {};

    items.map((item) => {
      const index = item.sku + "~" + warehouse_id;

      let price = item?.salePrice || item.sale_price || item.price;
      let disc = 0;
      if (channel === ForstokChannel.Tiktok) disc += item?.voucherSeller || item.voucher_seller || 0;

      price -= disc;

      if (!res[index]) {
        res[index] = {
          name: item.name,
          sku: item.sku,
          qty: item.qty || 1,
          price: item.price,
          sale_price: item?.salePrice || item.sale_price || item.price,
          total_price: price,
          promo_amount: 0,
          voucher_amount: 0,
          voucher_code: "",
          voucher_seller: 0,
          tax_price: item.tax_price,
          ordered_at,
        };
      } else {
        res[index].qty++;
        res[index].price += item.price;
        res[index].sale_price += item?.salePrice || item.sale_price || item.price;
        res[index].total_price += price;
      }
    });

    return res;
  }

  private async _createOrderOms(created: ForstokOrder, order: Record<string, any>, skus: Array<ISkus>) {
    let alreadyRts = false;

    if (this.autoRtsChannelList.includes(created.channelId)) {
      const logs = await this.redisRtsService.get(this.redisRtsKey + created.forstokOrderId);
      alreadyRts = logs === "success";
    }

    order.shipping_courier.shippingProvider = order.shipping_provider;
    order.shipping_courier.shippingProviderType = order.shipping_provider_type;
    order.shipping_courier.formattedAddress = order.address.formatted;

    // Override store information for Tiktok orders with Tokopedia in internal note
    let storeName = order.store_name;
    let storeId = order.store_id;
    let channel = order.channel;

    if (created.channelId === 10030 && order.internal_note) {
      try {
        const internalNoteObj = JSON.parse(order.internal_note);
        // Case insensitive check for "TOKOPEDIA" in commerce_platform
        if (
          internalNoteObj.commerce_platform &&
          internalNoteObj.commerce_platform.toUpperCase().includes("TOKOPEDIA")
        ) {
          storeName = "Tokopedia";
          storeId = 1141;
          channel = "tokopedia";
        }
      } catch (error) {
        // If JSON parsing fails, continue with original values
        console.log("Error parsing internalNote:", error);
      }
    }

    return await this.omsService.createOrder({
      forstok_id: created["_id"],
      shipping_courier: order.shipping_courier,
      shipping_price: order.shipping_price,
      tax_price: order.tax_price,
      total_price: order.total_price,
      customer_name: order.customer_info.name?.trim(),
      customer_phone: order.address.phone,
      store_name: storeName,
      store_id: storeId,
      channel: channel,
      shipping_address: order.address.formatted,
      address: order.address,
      marketplace_no: order.local_id,
      marketplace_name: order.local_name,
      payment_method: order.payment.payment_method,
      forstok_order_id: created.forstokOrderId,
      integration_date: created.integrationDate.toISOString(),
      payment_status: order.payment.status,
      sync_rts: alreadyRts,
      paid_at: created.paidAt ? created.paidAt.toISOString() : null,
      skus,
    });
  }

  private async _prepareToSave(order: Record<string, any>, saved: Record<string, any>): Promise<ForstokOrder> {
    const address = [order.address.address_1];

    if (order.address.address_2) address.push(order.address.address_2);
    if (order.address.subdistrict) address.push(order.address.subdistrict);
    if (order.address.disctrict) address.push(order.address.disctrict);
    if (order.address.city) address.push(order.address.city);
    if (order.address.province) address.push(order.address.province);
    if (order.address.postal_code) address.push(order.address.postal_code);

    order.address.formatted = address.toString();

    const expedition = [];
    if (order.shipping_provider) {
      const split = order.shipping_provider.split("Delivery: ");

      if (split.length > 1 && split[1]) {
        expedition.push(split[1]);
      } else {
        expedition.push(split[0]);
      }
    }
    if (order.shipping_provider_type && order.shipping_provider_type !== order.shipping_provider) {
      expedition.push(order.shipping_provider_type);
    }

    order.shipping_courier.expedition = expedition.join("-");

    order.shipping_courier.city = order.address.city;
    order.shipping_courier.postalCode = order.address.postal_code;

    if (order.shipping_courier.channel_docs_path && isBase64(order.shipping_courier.channel_docs_path)) {
      order.shipping_courier.channel_docs_path = null;
    }

    if (order.status === "Completed") order.status = ForstokStatus.Complete;
    const items = {};
    order.item_lines.map((item) => {
      if (!items[item.sku]) {
        items[item.sku] = {
          id: item.id,
          localId: item.local_id,
          sku: item.sku,
          name: item.name,
          variantName: item.variant_name,
          variantId: item.variant_id,
          variantSku: item.variant_sku,
          price: item.price,
          salePrice: item.sale_price,
          totalPrice: item.total_price,
          voucherAmount: item.voucher_amount,
          voucherCode: item.voucher_code,
          voucherSeller: item.voucher_seller,
          taxPrice: item.tax_price,
          fulfillByChannel: item.fulfill_by_channel,
          shippingProvider: item.shipping_provider,
          shippingProviderType: item.shipping_provider_type,
          trackingNumber: item.tracking_number,
          note: item.note,
          internalNote: item.internal_note,
          qty: item?.qty || 1,
        };
      } else {
        items[item.sku].qty++;
        items[item.sku].price += item.price;
        items[item.sku].salePrice += item.sale_price;
        items[item.sku].totalPrice += item.total_price;
      }
    });

    return {
      status: order.status,
      channel: order.channel,
      channelId: order.channel_id,
      localId: order.local_id,
      localName: order.local_name,
      storeName: order.store_name,
      storeId: order.store_id,
      profileId: order.profile_id,
      address: {
        address_1: order.address?.address_1,
        address_2: order.address?.address_2,
        city: order.address?.city,
        country: order.address?.country,
        name: order.address?.name?.trim(),
        phone: order.address?.phone,
        postalCode: order.address?.postal_code,
        province: order.address?.province,
        provinceCode: order.address?.province_code,
        subDistrict: order.address?.sub_district,
        district: order.address?.district,
        formatted: order.address?.formatted,
      },
      customerInfo: {
        id: order.customer_info.id,
        name: order.customer_info.name?.trim(),
        email: order.customer_info.email,
        customerSince: order.customer_info.customer_since,
      },
      orderedAt: order.ordered_at,
      createdAt: order.created_at,
      updatedAt: order.updated_at,
      itemLines: Object.values(items),
      forstokOrderId: order.id,
      integrationDate: saved?.integrationDate || new Date(),
      payment: {
        paymentMethod: order.payment.payment_method,
        status: order.payment.status,
      },
      shippingPrice: order.shipping_price,
      discShippingSeller: order.disc_shipping_seller,
      discShippingPlatform: order.disc_shipping_platform,
      shippingCourier: {
        awb: saved?.awb || order.shipping_courier.awb,
        documentPath: order.shipping_courier.document_path,
        bookingCode: order.shipping_courier.booking_code,
        deliveryType: order.shipping_courier.delivery_type,
        channelDocsPath: order.shipping_courier.channel_docs_path,
        logisticDestinationCode: order.shipping_courier.logistic_destination_code,
        expedition: order.shipping_courier.expedition,
        shippingProvider: order.shipping_provider || saved?.shippingProvider,
        shippingProviderType: order.shipping_provider_type || saved?.shippingProviderType,
        formattedAddress: order.formattedAddress,
        city: order.address.city,
        postalCode: order.address.postal_code,
      },
      shippingProvider: order.shipping_provider || saved?.shippingProvider,
      shippingProviderType: order.shipping_provider_type || saved?.shippingProviderType,
      shippingDescription: order.shipping_description,
      subtotal: order.subtotal,
      channelRebate: order.channel_rebate,
      cashless: order.cashless,
      discountAmount: order.discount_amount,
      totalPrice: order.total_price,
      voucherCode: order.voucher_code,
      voucherSeller: order.voucher_seller,
      insuranceFee: order.insurance_fee,
      discountReason: order.discount_reason,
      taxPrice: order.tax_price,
      warehouseId: order.warehouse_id,
      cod: order.cod,
      deliveryType: order.delivery_type,
      warehouseCode: order.warehouse_code,
      note: order.note,
      internalNote: order.internal_note,
      paidAt: !saved?.paidAt && order.status === ForstokStatus.Open ? new Date() : saved?.paidAt || undefined,
    };
  }

  private async _setRts(orderId: number, channelId: string) {
    if (process.env.NODE_ENV.toUpperCase() !== "PRODUCTION") {
      return;
    }
    // generate forstok token
    const forstokToken = await this.forstokService.getAuthToken();

    // get all configs
    const [withConfirmOrder, withPickupAddress, withTimeslot] = await Promise.all([
      this.utilService.getConfig("mp.must_create_shipment_first"),
      this.utilService.getConfig("mp.pick_address_first"),
      this.utilService.getConfig("mp.pick_timeslot_first"),
    ]);

    // extract config data from string to array
    const confirmOrderList = stringToArray(withConfirmOrder, ",");
    const pickupAddressList = stringToArray(withPickupAddress, ",");
    const timeslotList = stringToArray(withTimeslot, ",");

    // run this block when channel id match with config
    if (confirmOrderList.includes(channelId)) {
      // check to redis is this order already confirmed to forstok
      const alreadyConfirm = await this.redisRtsService.get(this.redisConfirmKey + orderId);
      if (!alreadyConfirm) {
        // if not then request confirm to forstok
        const confirm = await this.forstokService.confirmOrder(orderId, forstokToken);
        // check is request to forstok success, then set to redis
        if (confirm?.status === "Success") await this.redisRtsService.set(this.redisConfirmKey + orderId, "success");
        else throw Error(`Confirm order ${orderId} failed`);
      }
    }

    // run this block when channel id match with config
    let pickupAddressChosen = "";
    if (pickupAddressList.includes(channelId)) {
      // request pickup address list to forstok
      const pickupAddress = await this.forstokService.getPickupAddress(orderId, forstokToken);
      // pick only first data when data returned is success
      if (pickupAddress?.[0]?.address_id) pickupAddressChosen = pickupAddress[0].address_id;
    }

    // run this block when channel id match with config
    let timeslotChosen = "";
    if (timeslotList.includes(channelId)) {
      // request pickup timeslot list to forstok
      const timeslot = await this.forstokService.getPickupTimeslot(orderId, pickupAddressChosen, forstokToken);
      // pick only first data when data returned is success
      if (timeslot?.[0]?.pickup_time_id) timeslotChosen = timeslot[0].pickup_time_id;
    }

    // request set order status to ready to ship to forstok
    const rts = await this.forstokService.setReadyToShip({
      token: forstokToken,
      pickupAddressId: pickupAddressChosen,
      pickupTimeslotId: timeslotChosen,
      orderId,
    });

    // check is request status already change the order status
    if (rts?.status === ForstokStatus.ReadyToShip) {
      // set data to redis
      await this.redisRtsService.set(this.redisRtsKey + orderId, "success");
    }
  }

  private async _getProcessedChannelList() {
    const data = await this.utilService.getConfig("mp.process_channel");

    if (data) {
      this.processedChannelList = stringToArray(data, ",");
    }
  }

  private async _getAutoRtsChannelList() {
    const data = await this.utilService.getConfig("mp.auto_rts_channel_forstok");

    if (data) this.autoRtsChannelList = stringToArray(data, ",");
  }

  private async _delRtsFromRedis(orderId: string) {
    return await Promise.all([
      this.redisRtsService.del(this.redisConfirmKey + orderId),
      this.redisRtsService.del(this.redisRtsKey + orderId),
    ]);
  }
}
