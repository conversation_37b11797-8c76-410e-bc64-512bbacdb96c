import { DEFAULT_REDIS_NAMESPACE, RedisModule } from "@liaoliaots/nestjs-redis";
import { HttpModule } from "@nestjs/axios";
import { Module } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { MongooseModule } from "@nestjs/mongoose";
import { RedisNsAutoRts } from "../../enum/callback.enum";
import { ForstokModule } from "../forstok/forstok.module";
import { GoldModule } from "../gold/gold.module";
import { ForstokOrderError, ForstokOrderErrorSchema } from "../marketplace/schema/forstok-order-error.schema";
import { ForstokOrder, ForstokOrderSchema } from "../marketplace/schema/forstok-order.schema";
import { ForstokTempStock, ForstokTempStockSchema } from "../marketplace/schema/forstok-temp-stock";
import {
  MarketplaceAggregatorError,
  MarketplaceAggregatorErrorSchema,
} from "../marketplace/schema/marketplace-aggregator-error.schema";
import {
  MarketplaceAggregator,
  MarketplaceAggregatorSchema,
} from "../marketplace/schema/marketplace-aggregator.schema";
import { ApiOmsModule } from "../microservices/oms/api-oms.module";
import { ApiUtilsModule } from "../microservices/utils/api-utils.module";
import { CallbackQueueWorkerV1Service } from "./callback-queue-worker-v1.service";
import { CallbackQueueWorkerService } from "./callback-queue-worker.service";
import { CallbackV1Controller } from "./callback-v1.controller";
import { CallbackV1Service } from "./callback-v1.service";
import { CallbackController } from "./callback.controller";
import { CallbackService } from "./callback.service";
import {
  CallbackMarketplaceAggregator,
  CallbackMarketplaceAggregatorSchema,
} from "./schema/callback-marketplace-aggregator.schema";
import { Callback, CallbackSchema } from "./schema/callback.schema";

@Module({
  controllers: [CallbackController, CallbackV1Controller],
  providers: [CallbackService, CallbackV1Service, CallbackQueueWorkerService, CallbackQueueWorkerV1Service],
  imports: [
    ForstokModule,
    ApiOmsModule,
    ApiUtilsModule,
    ConfigModule,
    GoldModule,
    MongooseModule.forFeature([
      {
        name: Callback.name,
        schema: CallbackSchema,
      },
      {
        name: ForstokOrder.name,
        schema: ForstokOrderSchema,
      },
      {
        name: ForstokOrderError.name,
        schema: ForstokOrderErrorSchema,
      },
      {
        name: ForstokTempStock.name,
        schema: ForstokTempStockSchema,
      },
      {
        name: CallbackMarketplaceAggregator.name,
        schema: CallbackMarketplaceAggregatorSchema,
      },
      {
        name: MarketplaceAggregatorError.name,
        schema: MarketplaceAggregatorErrorSchema,
      },
      {
        name: MarketplaceAggregator.name,
        schema: MarketplaceAggregatorSchema,
      },
    ]),
    RedisModule.forRoot({
      config: [
        {
          port: +process.env.REDIS_MP_PORT,
          host: process.env.REDIS_MP_HOST,
          db: +process.env.REDIS_MP_DB,
          namespace: DEFAULT_REDIS_NAMESPACE,
        },
        {
          port: +process.env.REDIS_RTS_FORSTOK_PORT,
          host: process.env.REDIS_RTS_FORSTOK_HOST,
          db: +process.env.REDIS_RTS_FORSTOK_DB,
          namespace: RedisNsAutoRts,
        },
      ],
    }),
    HttpModule.register({ timeout: 3000 }),
  ],
  exports: [CallbackService, CallbackV1Service],
})
export class CallbackModule {}
