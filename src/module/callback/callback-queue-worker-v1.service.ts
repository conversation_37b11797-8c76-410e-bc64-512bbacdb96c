import { DEFAULT_REDIS_NAMESPACE, InjectRedis } from "@liaoliaots/nestjs-redis";
import { ConfigService } from "@nestjs/config";
import { InjectModel } from "@nestjs/mongoose";
import { Interval } from "@nestjs/schedule";
import * as dayjs from "dayjs";
import Redis from "ioredis";
import { Model, PaginateModel } from "mongoose";
import { channelId } from "src/enum/channel-id.enum";
import { RedisNsAutoRts } from "../../enum/callback.enum";
import {
  ForstokStatus,
  MarketplaceStatus,
  MarketplaceStatusPriority,
  OMSOrderStatus,
  PaymentStatus,
} from "../../enum/forstok-salesorder-status.enum";
import { stringToArray } from "../../utils/function.util";
import { ForstokService } from "../forstok/forstok.service";
import { ForstokTempStock, ForstokTempStockDocument } from "../marketplace/schema/forstok-temp-stock";
import {
  MarketplaceAggregatorError,
  MarketplaceAggregatorErrorDocument,
} from "../marketplace/schema/marketplace-aggregator-error.schema";
import {
  MarketplaceAggregator,
  MarketplaceAggregatorDocument,
} from "../marketplace/schema/marketplace-aggregator.schema";
import { ISkus } from "../microservices/oms/api-oms.interface";
import { ApiOmsService } from "../microservices/oms/api-oms.service";
import { ApiUtilsService } from "../microservices/utils/api-utils.service";
import {
  CallbackMarketplaceAggregator,
  CallbackMarketplaceAggregatorDocument,
} from "./schema/callback-marketplace-aggregator.schema";

export class CallbackQueueWorkerV1Service {
  private thisPodsProcessQueue = false;
  private processedChannelList = [];
  private autoRtsChannelList = [];
  private readonly redisRtsKey = "rts-";
  private readonly redisConfirmKey = "confirm-";
  private readonly availableListKey = "order-available-queue";
  private readonly processedListKey = "order-processed-queue";
  private readonly lockedListKey = "order-locked-queue";
  private readonly waitingListKey = "order-waiting-queue";
  private readonly autoReleaseKey = "order-auto-release-queue";

  constructor(
    @InjectModel(CallbackMarketplaceAggregator.name)
    private readonly callback: Model<CallbackMarketplaceAggregatorDocument>,
    @InjectModel(MarketplaceAggregator.name)
    private marketplaceOrderModel: PaginateModel<MarketplaceAggregatorDocument>,
    @InjectModel(MarketplaceAggregatorError.name)
    private marketplaceOrderErrorModel: PaginateModel<MarketplaceAggregatorErrorDocument>,
    @InjectModel(ForstokTempStock.name)
    private forstokTempStockModel: Model<ForstokTempStockDocument>,
    @InjectRedis(DEFAULT_REDIS_NAMESPACE)
    private readonly redisOrderService: Redis,
    @InjectRedis(RedisNsAutoRts)
    private readonly redisRtsService: Redis,
    private readonly configService: ConfigService,
    private readonly forstokService: ForstokService,
    private readonly omsService: ApiOmsService,
    private readonly utilService: ApiUtilsService,
  ) {}

  async addToQueue(id: string, status: string, callback_id: string) {
    const script = `
      local availList = KEYS[1]
      local workedList = KEYS[2]
      local waitingList = KEYS[3]
      
      local forstokId = ARGV[1]
      local callbackId = ARGV[2]
      
      local isWaiting = false
      local keyPrefix = 'orders:'
      
      -- check when forstokId already processed on queue then must be waiting until it finish before can be processed again
      if redis.call('LPOS', workedList, forstokId) then
        if not redis.call('LPOS', waitingList, forstokId) then
          redis.call('RPUSH', waitingList, forstokId)
        end
        keyPrefix = keyPrefix .. 'waiting-'
        isWaiting = true
      end
      
      -- check when forstokId doesn't exists on available list then push it
      if isWaiting == false and not redis.call('LPOS', availList, forstokId) then
        redis.call('RPUSH', availList, forstokId)
      end
      
      -- push callback_id to order key list
      return redis.call('RPUSH', keyPrefix .. forstokId, callbackId)
    `;

    this.redisOrderService.eval(
      script,
      3,
      this.availableListKey,
      this.processedListKey,
      this.waitingListKey,
      id,
      callback_id,
    );

    return this.runQueue();
  }

  @Interval(20)
  async runQueue() {
    if (this.thisPodsProcessQueue) return;

    this.thisPodsProcessQueue = true;
    await this._autoReleaseLock();

    const timestamp = Date.now();
    const script = `
      local availListKey = KEYS[1]
      local workedListKey = KEYS[2]
      local lockListKey = KEYS[3]
      
      local timestamp = ARGV[1]
      
      local idToProceed = {}
      
      for i=0, 10 do
        local id = redis.call('LPOP', availListKey)
        
        if not id then
          break
        end
        
        local lockVal = {}
        lockVal['id'] = id
        lockVal['timestamp'] = timestamp
        
        redis.call('RPUSH', lockListKey, cjson.encode(lockVal))
        redis.call('RPUSH', workedListKey, id)
        
        table.insert(idToProceed, id)
      end
      
      return idToProceed
    `;

    const forstokIds = (await this.redisOrderService.eval(
      script,
      3,
      this.availableListKey,
      this.processedListKey,
      this.lockedListKey,
      timestamp,
    )) as Array<string>;

    await Promise.all(
      forstokIds?.map(async (forstokId) => {
        let lists = await this.redisOrderService.lrange(`orders:${forstokId}`, 0, -1);

        try {
          const callbacks = await this.callback.find({ _id: lists }).lean();

          if (!callbacks?.length) return;

          let sorted = [];
          let channelId = "";
          callbacks.map((cb) => {
            const priority = MarketplaceStatusPriority[cb.orderStatus !== "Completed" ? cb.orderStatus : "Complete"];
            sorted.push({ ...cb, priority });
          });

          sorted = sorted.sort(
            (a, b) => a.priority - b.priority || dayjs(a.updated_at).unix() - dayjs(b.updated_at).unix(),
          );

          const [defaultCourier] = await Promise.all([
            this.utilService.getConfig(`mp.default_courier.${channelId}`),
            this._getProcessedChannelList(),
            this._getAutoRtsChannelList(),
          ]);

          for (const cb of sorted) {
            try {
              // if (defaultCourier) {
              //   cb.shipment[0].shippingCarrier = defaultCourier.shippingProvider;
              //   cb.shipment[0].shippingCarrier = defaultCourier.shippingProviderType;
              // }

              await this.processData(cb, null);

              cb.status = true;
              lists = lists.filter((a) => a !== cb._id.toString());
            } catch (err) {
              console.dir({
                title: "Error processData",
                err: err.message,
              });
              cb.status = false;
              cb.error_message = err.message;
            } finally {
              await this.callback.updateOne({ _id: cb._id }, cb);
            }
          }
        } catch (err) {
          console.dir({
            title: "Error process queue",
            err,
          });
        } finally {
          await this._clearLock(forstokId, lists, timestamp);
        }
      }),
    );

    this.thisPodsProcessQueue = false;
  }

  private async _clearLock(forstokId: string, lists: Array<string>, timestamp: number) {
    const script = `
      local waitingKey = KEYS[1]
      local availableKey = KEYS[2]
      local lockKey = KEYS[3]
      local processKey = KEYS[4]
      
      local id = ARGV[1]
      local list = cjson.decode(ARGV[2])
      local lockVal = ARGV[3]
      
      local inWaiting = redis.call('LREM', waitingKey, 1, id)
            
      if inWaiting > 0 then
        local listWaiting = redis.call('LRANGE', 'orders:waiting-' .. id, 0, -1)
        redis.call('DEL', 'orders:waiting-' .. id)
        redis.call('RPUSH', availableKey, id)

        if #listWaiting > 0 then
          for _, val in ipairs(listWaiting) do
            table.insert(list, val)
          end
        end
      end
      
      redis.call('DEL', 'orders:' .. id)

      if #list > 0 then
        for _, val in ipairs(list) do
          redis.call('RPUSH', 'orders:' .. id, val)
        end
      end
      
      redis.call('LREM', processKey, 1, id)
      redis.call('LREM', lockKey, 1, lockVal)
    `;

    await this.redisOrderService.eval(
      script,
      4,
      this.waitingListKey,
      this.availableListKey,
      this.lockedListKey,
      this.processedListKey,
      forstokId,
      JSON.stringify(lists),
      JSON.stringify({ id: forstokId + "", timestamp: timestamp + "" }),
    );
  }

  private async _autoReleaseLock() {
    const lastRelease = await this.redisOrderService.get(this.autoReleaseKey);

    if (lastRelease && dayjs().diff(dayjs(Number(lastRelease)), "minutes") < 1) {
      return;
    }

    await this.redisOrderService.set(this.autoReleaseKey, Date.now());

    const maxLock = (await this.utilService.getConfig("mp.queue-max-lock")) || "15 minute";
    const [value, unit] = maxLock.split(" ");

    const list = await this.redisOrderService.lrange(this.lockedListKey, 0, -1);

    for (const item of list) {
      const { id, timestamp } = JSON.parse(item);

      if (dayjs(timestamp) < dayjs().subtract(value, unit)) {
        await Promise.all([
          this.redisOrderService.lrem(this.lockedListKey, 1, item),
          this.redisOrderService.lrem(this.processedListKey, 1, id),
        ]);

        const inWaiting = await this.redisOrderService.lrem(this.waitingListKey, 1, id);

        if (inWaiting) {
          const [listCbWaiting, listCb] = await Promise.all([
            this.redisOrderService.lrange(`orders:waiting-${id}`, 0, -1),
            this.redisOrderService.lrange(`orders:${id}`, 0, -1),
            this.redisOrderService.rpush(this.availableListKey, id),
          ]);

          const newList = [...(listCb || []), ...(listCbWaiting || [])];
          if (newList.length) {
            await this.redisOrderService.del(`orders:${id}`);
            await this.redisOrderService.rpush(`orders:${id}`, ...newList);
          }
        }
      }
    }
  }

  //changes
  async processData(order: Record<string, any>, gracePeriod: Date) {
    if (order.payment.totalAmount < 1 && !order.force_push) return;

    this._getAutoRtsChannelList();

    let mock;
    let lastStatusPriority = MarketplaceStatusPriority[order.orderStatus]; // for save the last status before changed below

    let lastStatus = order.orderStatus;
    const existData = await this.marketplaceOrderModel.findOne({ maId: order.maId }).lean();

    try {
      if (existData) {
        lastStatus = existData.orderStatus;
        lastStatusPriority = MarketplaceStatusPriority[existData.orderStatus];
      }
      mock = await this._prepareToSave(order, existData);

      if (order.orderStatus === MarketplaceStatus.Cancelled) {
        if (existData) {
          await this._handleCancelled(existData.orderNumber, existData.updatedAt.toISOString(), existData.id + "");
          return await this.marketplaceOrderModel.findByIdAndUpdate(existData._id, {
            status: MarketplaceStatus.Cancelled,
          });
        } else {
          return await this.marketplaceOrderModel.create(mock);
        }
      }

      if (existData?.orderStatus === MarketplaceStatus.Cancelled) return;

      order.shipment[0].recipient.city = order.shipment[0].recipient.city;
      order.shipment[0].recipient.zipcode = order.shipment[0].recipient.city;
      const saved = (await this.marketplaceOrderModel.findOneAndUpdate({ maId: order.maId }, mock, {
        upsert: true,
        new: true,
        lean: true,
      })) as any;

      if (mock.orderStatus === "COMPELETED") mock.orderStatus = MarketplaceStatus.Complete;

      const compareDate = saved.paidAt || saved.orderedAt;

      if (!gracePeriod || new Date(compareDate) <= gracePeriod) {
        switch (order.orderStatus) {
          case MarketplaceStatus.NotPaid:
            await this._processOpenOrder(order, saved, lastStatus, lastStatusPriority);

            break;
          case MarketplaceStatus.Open:
            await this._processOpenOrder(order, saved, lastStatus, lastStatusPriority);
            break;
          case MarketplaceStatus.NotShipped:
            await this._processOpenOrder(order, saved, lastStatus, lastStatusPriority);
            break;
          case MarketplaceStatus.Printed:
            await this._processOpenOrder(order, saved, lastStatus, lastStatusPriority);
            break;
          case MarketplaceStatus.ReadyToShip:
            await this._processOpenOrder(order, saved, lastStatus, lastStatusPriority);
            break;
          case MarketplaceStatus.Shipped:
            await this._handleShipped(
              saved.orderNumber,
              saved.updatedAt.toISOString(),
              saved.shippingCourier,
              saved.shippingPrice,
              saved._id + "",
            );
            break;
          case MarketplaceStatus.Delivered:
            await this._handleDelivered(saved.orderNumber, saved.updatedAt.toISOString(), saved.maId + "");
            break;
          case MarketplaceStatus.Complete:
            await this._handleComplete(saved.orderNumber, saved.updatedAt.toISOString(), saved.maId + "");
            break;
          case MarketplaceStatus.Cancelled:
            await this._handleCancelled(saved.orderNumber, saved.updatedAt.toISOString(), saved.maId + "");
            await this.marketplaceOrderModel.updateOne({ _id: saved._id }, { status: ForstokStatus.Cancelled });
            break;
          default:
            break;
        }
      }

      await this.marketplaceOrderErrorModel.deleteOne({ maId: order.maId });
    } catch (err) {
      console.log(err);

      if (existData) await this.marketplaceOrderModel.updateOne({ _id: existData._id }, existData);
      await this._handleErrorProcess(mock, err?.message || err);
    }
  }

  private async _processOpenOrder(
    order: Record<string, any>,
    saved: MarketplaceAggregatorDocument,
    lastStatus: string,
    lastStatusPriority: string,
  ) {
    await this._handleOpen(order, saved);
    const cbStatusPriority = MarketplaceStatusPriority[order.orderStatus];
    let newStatus = saved.orderStatus;

    // set back status to last status when current callback status less than last db status priority
    // ex: cb status = Printed (priority 2) db status Ready to Pick (priority 4)
    // then this if will set back status to Ready to Pick
    if (lastStatusPriority > cbStatusPriority) newStatus = lastStatus;
    return this.marketplaceOrderModel.updateOne({ _id: saved._id }, { status: newStatus });
  }

  private async _handleOpen(order: Record<string, any>, saved: MarketplaceAggregatorDocument) {
    try {
      const items = this._mapItemForOms(
        saved.itemLines,
        saved.warehouseId,
        saved.orderedAt.toISOString(),
        saved.marketplaceCode,
      );
      const _channelId = saved.marketplaceCode.toString();

      if (this.autoRtsChannelList.includes(_channelId)) {
        await this._setRts(saved._id, _channelId);
      }

      let orderCreated;
      if (!saved.orderNumber && this.processedChannelList.includes(String(order.marketplaceCode))) {
        orderCreated = await this._createOrderOms(saved, order, Object.values(items));
      }

      const mock: Record<string, any> = { orderNumber: saved.orderNumber || orderCreated?.orderNumber };

      if (order.payment.paymentStatus === PaymentStatus["PAID"]) {
        await this.omsService.updateOrder({
          orderNumber: mock.orderNumber,
          updatedAt: saved.updatedAt.toISOString(),
          status: OMSOrderStatus.Paid,
          forstok_shipping_courier: saved.shippingCourier,
          shippingPrice: saved.shippingPrice,
          paidAt: saved.paidAt.toISOString(),
        });
      }

      return await this.marketplaceOrderModel.updateOne({ _id: saved._id }, mock);
    } catch (err) {
      console.log(err);
      throw err.response?.data?.message || err.message;
    }
  }

  private async _handleShipped(
    orderNumber: string,
    updatedAt: string,
    forstok_shipping_courier: Record<string, any>,
    shippingPrice: number,
    orderId: string,
  ) {
    if (orderNumber) {
      await this.omsService.updateOrder({
        orderNumber,
        updatedAt,
        status: OMSOrderStatus.OnShipment,
        forstok_shipping_courier,
        shippingPrice,
      });
      await this.marketplaceOrderModel.updateOne({ orderNumber }, { syncRts: true });
      return await this._delRtsFromRedis(orderId);
    }
  }

  private async _handleDelivered(orderNumber: string, updatedAt: string, orderId: string) {
    if (orderNumber) {
      await this.omsService.updateOrder({
        orderNumber,
        updatedAt,
        status: OMSOrderStatus.Delivered,
      });
      return await this._delRtsFromRedis(orderId);
    }
  }

  private async _handleComplete(orderNumber: string, updatedAt: string, orderId: string) {
    if (orderNumber) {
      await this.omsService.updateOrder({
        orderNumber,
        updatedAt,
        status: OMSOrderStatus.Complete,
      });
      return await this._delRtsFromRedis(orderId);
    }
  }

  private async _handleCancelled(orderNumber: string, updatedAt: string, orderId: string) {
    if (orderNumber) {
      await this.omsService.updateOrder({
        orderNumber,
        updatedAt,
        status: OMSOrderStatus.Cancelled,
      });
      return await this._delRtsFromRedis(orderId);
    }
  }

  private async _handleErrorProcess(order: Record<string, any>, err: string) {
    await this.marketplaceOrderModel.deleteOne({ maId: order.maId });

    return await this.marketplaceOrderErrorModel
      .findOneAndUpdate({ maId: order.maId }, { ...order, errorMessage: err }, { upsert: true, new: true })
      .exec();
  }

  private _mapItemForOms(
    items: Array<Record<string, any>>,
    warehouse_id: number,
    ordered_at: string,
    channel: string,
  ): Record<string, ISkus> {
    const res: Record<string, ISkus> = {};

    items.map((item) => {
      const index = item.sku;

      let price = item?.originalPrice || item.originalPrice || item.originalPrice;
      let disc = 0;
      let voucherCode = "";

      price -= disc;
      if (item.sellerDiscountBreakdown) {
        item.sellerDiscountBreakdown.forEach((disc, index) => {
          if (disc.discountType === "VOUCHER") {
            voucherCode += disc.discountCode;
            if (index < item.sellerDiscountBreakdown.length - 1) {
              voucherCode += ",";
            }
          }
        });
      }

      if (!res[index]) {
        res[index] = {
          name: item.name,
          sku: item.sku,
          qty: Number(item.qty) || 1,
          price: item.price,
          sale_price: item?.price || item.totalPrice || item.price,
          total_price: item?.totalPrice,
          promo_amount: item.sellerDiscount,
          voucher_amount: 0,
          voucher_code: voucherCode,
          voucher_seller: item.sellerVoucherDiscount,
          tax_price: Number(item.tax),
          ordered_at,
        };
      } else {
        res[index].qty++;
        res[index].price += item.totalPrice;
        res[index].sale_price += item?.price || item.price || item.price;
        res[index].total_price += price;
      }
    });

    return res;
  }

  private async _createOrderOms(created: MarketplaceAggregator, order: Record<string, any>, skus: Array<ISkus>) {
    let alreadyRts = true;

    return await this.omsService.createOrderMarketplace({
      marketplace_aggregator_id: created["_id"],
      shipping_courier: created.shippingCourier,
      shipping_price: created.payments.shipmentFee,
      tax_price: 0,
      total_price: created.payments.totalAmount,
      customer_name: created.customerInfo.name?.trim(),
      customer_phone: created.address.phone,
      store_name: created.marketplaceCode,
      store_id: created.storeId,
      channel: created.marketplaceCode,
      shipping_address: created.address.formatted,
      address: created.address,
      marketplace_no: created.orderId,
      marketplace_name: created.orderId,
      payment_method: created.payments.method,
      integration_date: created.integrationDate.toISOString(),
      payment_status: PaymentStatus[created.payments.paymentStatus],
      sync_rts: alreadyRts,
      paid_at: created.paidAt ? created.paidAt.toISOString() : null,
      skus,
      maId: created?.maId,
    });
  }

  // Samain kayak table marketplaceAggregator
  private async _prepareToSave(order: Record<string, any>, saved: Record<string, any>): Promise<MarketplaceAggregator> {
    if (order.orderStatus === "Completed") order.orderStatus = ForstokStatus.Complete;
    let split = [];
    if (order.shipment[0].shippingCarrier) {
      split = order?.shipment[0].shippingCarrier.split("-");
    }

    const items = {};
    order.items.map((item) => {
      let voucherCodeSplit = "";
      if (item.sellerDiscountBreakdown) {
        item.sellerDiscountBreakdown.forEach((disc, index) => {
          if (disc.discountType === "VOUCHER") {
            voucherCodeSplit += disc.discountCode;
            if (index < item.sellerDiscountBreakdown.length - 1) {
              voucherCodeSplit += ",";
            }
          }
        });
      }

      if (item.variantSku) {
        if (!items[item.variantSku]) {
          items[item.variantSku] = {
            id: item.itemId,
            sku: item.variantSku,
            name: item.variantName,
            variantName: item.variantName,
            variantId: item.variantId,
            variantSku: item.variantSku,
            price: item.originalPrice,
            salePrice: item.originalPrice || item.originalPrice,
            totalPrice: item.grandTotal,
            taxPrice: item.tax,
            note: item.note,
            promotion: item.promotion,
            qty: item.quantity || 1,
            imageUrl: item.imageUrl,
            weight: item.weight,
            sellerDiscount: item.sellerDiscount,
            marketplaceDiscount: item.marketplaceDiscount,
            sellerVoucherDiscount: item.sellerVoucherDiscount,
            sellerDiscountBreakdown: item.sellerDiscountBreakdown,
            packageStatus: item.packageStatus,
            isGift: item.isGift,
            coinRebate: item.coinRebate,
            voucherCode: voucherCodeSplit,
          };
        } else {
          items[item.variantSku].qty += Number(item.quantity);
          items[item.variantSku].price += item.originalPrice;
          items[item.variantSku].salePrice += item.originalPrice || item.grandTotal;
          items[item.variantSku].totalPrice += item.grandTotal;
        }
      } else {
        if (!items[item.mpSku]) {
          items[item.mpSku] = {
            id: item.itemId,
            sku: item.mpSku,
            name: item.itemName,
            variantName: item.variantName,
            variantId: item.variantId,
            variantSku: item.variantSku,
            price: item.originalPrice,
            salePrice: item.originalPrice || item.originalPrice,
            totalPrice: item.grandTotal,
            taxPrice: item.tax,
            note: item.note,
            promotion: item.promotion,
            qty: item.quantity || 1,
            imageUrl: item.imageUrl,
            weight: item.weight,
            sellerDiscount: item.sellerDiscount,
            marketplaceDiscount: item.marketplaceDiscount,
            sellerVoucherDiscount: item.sellerVoucherDiscount,
            sellerDiscountBreakdown: item.sellerDiscountBreakdown,
            packageStatus: item.packageStatus,
            isGift: item.isGift,
            coinRebate: item.coinRebate,
          };
        } else {
          items[item.mpSku].qty += Number(item.quantity);
          items[item.mpSku].price += item.originalPrice;
          items[item.mpSku].salePrice += item.originalPrice || item.grandTotal;
          items[item.mpSku].totalPrice += item.grandTotal;
        }
      }
    });

    const orderStatusPaid = [ForstokStatus.Open, ForstokStatus.Printed, ForstokStatus.ReadyToShip];

    return {
      storeId: channelId[order.marketplaceCode as keyof typeof channelId],
      maId: order.maId,
      companyId: order.companyId,
      orderStatus: order.orderStatus,
      channel: order.marketplaceCode,
      channelId: channelId[order.marketplaceCode as keyof typeof channelId],
      orderId: order.orderId,
      marketplaceCode: order.marketplaceCode,
      address: {
        address_1: order.shipment[0].recipient.fullAddress,
        city: order.shipment[0].recipient.city,
        name: order.shipment[0].recipient?.name?.trim(),
        phone: order.shipment[0].recipient.phone,
        postalCode: order.shipment[0].recipient.zipcode,
        province: order.shipment[0].recipient.region,
        district: order.shipment[0].recipient?.district,
        formatted: order.shipment[0].recipient?.fullAddress,
      },
      customerInfo: {
        id: null,
        name: order.shipment[0].recipient?.name?.trim(),
        email: order.shipment[0].recipient?.email || "",
        customerSince: order.shipment[0].recipient?.customer_since || null,
      },
      orderedAt: order.timeline.orderTime,
      createdAt: order.createdAt,
      updatedAt: order.updatedAt,
      itemLines: Object.values(items),
      integrationDate: saved?.integrationDate || new Date(),
      payments: {
        method: order.payment.method,
        totalAmount: order.payment.totalAmount,
        discountSellerAmount: order.payment.discountSellerAmount,
        voucherSellerDiscount: order.payment.voucherSellerDiscount,
        discountPlatformAmount: order.payment.discountPlatformAmount,
        voucherPlatformDiscount: order.payment.voucherPlatformDiscount,
        paymentStatus: order.payment.paymentStatus,
        totalFee: order.payment.totalFee,
        shipmentFee: order.payment.shipmentFee,
        shipmentFeeDiscount: order.payment.shipmentFeeDiscount,
        subtotal: order.payment.subtotal,
        feeBreakdown: order.payment.feeBreakdown,
        discountBreakdown: order.payment.discountBreakdown,
      },
      shippingPrice: order.shipment[0].actualShippingFee,
      shippingCourier: {
        awb: saved?.awb || order.shipment[0].trackingNumber,
        documentPath: order.shipment[0]?.documentPath || "",
        bookingCode: order.shipment[0]?.bookingCode || "",
        deliveryType: order.shipment[0]?.shippingCarrier,
        channelDocsPath: order.shipment[0].channelDocsPath || "",
        logisticDestinationCode: order.shipment[0]?.logistic_destination_code || "",
        expedition: order.shipment[0]?.shippingCarrier,
        formattedAddress: order.shipment[0].recipient?.fullAddress,
        city: order.shipment[0].recipient.city,
        postalCode: order.shipment[0].recipient.zipcode,
        shippingProvider: split[0] || "",
        shippingProviderType: split[1] || "",
      },
      warehouseId: order?.warehouseId || 195,
      shippingProvider: split[0] || "",
      shippingProviderType: split[1] || "",
      paidAt: !saved?.paidAt && orderStatusPaid.includes(order.orderStatus) ? new Date() : saved?.paidAt || new Date(),
    };
  }

  private async _setRts(orderId: number, channelId: string) {
    if (process.env.NODE_ENV.toUpperCase() !== "PRODUCTION") {
      return;
    }
    // generate forstok token
    const forstokToken = await this.forstokService.getAuthToken();

    // get all configs
    const [withConfirmOrder] = await Promise.all([this.utilService.getConfig("mp.must_create_shipment_first")]);

    // extract config data from string to array
    const confirmOrderList = stringToArray(withConfirmOrder, ",");

    // run this block when channel id match with config
    if (confirmOrderList.includes(channelId)) {
      // check to redis is this order already confirmed to forstok
      const alreadyConfirm = await this.redisRtsService.get(this.redisConfirmKey + orderId);
      if (!alreadyConfirm) {
        // if not then request confirm to forstok
        const confirm = await this.forstokService.confirmOrder(orderId, forstokToken);
        // check is request to forstok success, then set to redis
        if (confirm?.status === "Success") await this.redisRtsService.set(this.redisConfirmKey + orderId, "success");
        else throw Error(`Confirm order ${orderId} failed`);
      }
    }

    // request set order status to ready to ship to forstok
    const rts = await this.forstokService.setReadyToShip({
      token: forstokToken,
      orderId,
    });

    // check is request status already change the order status
    if (rts?.status === ForstokStatus.ReadyToShip) {
      // set data to redis
      await this.redisRtsService.set(this.redisRtsKey + orderId, "success");
    }
  }

  private async _getProcessedChannelList() {
    const data = await this.utilService.getConfig("mp.process_channel_marketplace");

    if (data) {
      this.processedChannelList = stringToArray(data, ",");
    }
  }

  private async _getAutoRtsChannelList() {
    const data = await this.utilService.getConfig("mp.auto_rts_channel_forstok");

    if (data) this.autoRtsChannelList = stringToArray(data, ",");
  }

  private async _delRtsFromRedis(orderId: string) {
    return await Promise.all([
      this.redisRtsService.del(this.redisConfirmKey + orderId),
      this.redisRtsService.del(this.redisRtsKey + orderId),
    ]);
  }
}
