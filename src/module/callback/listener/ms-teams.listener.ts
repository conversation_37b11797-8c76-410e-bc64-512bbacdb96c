import { Injectable } from "@nestjs/common";
import { OnEvent } from "@nestjs/event-emitter";
import { ISendErrorMsTeams, MsTeamsListenerEvent } from "../../../enum/ms-teams-listener.enum";
import * as dayjs from "dayjs";
import { HttpService } from "@nestjs/axios";
import { wait } from "../../../utils/function.util";

@Injectable()
export class MsTeamsListener {
  constructor(private readonly httpService: HttpService) {}

  @OnEvent(MsTeamsListenerEvent.send_error)
  async sendError(payload: ISendErrorMsTeams) {
    const { errorMessage, data } = payload;
    const maxRetries = 3;

    let attempt = 0;
    while (++attempt <= maxRetries) {
      try {
        const url = process.env.MS_TEAMS_INCOMING_WEBHOOK;

        const cardData = {
          "@type": "MessageCard",
          "@context": "http://schema.org/extensions",
          themeColor: "df4759",
          summary: "Error while processing marketplace order",
          sections: [
            {
              activityTitle: "Error while processing marketplace order with details:",
              facts: [
                { name: "Forstok ID", value: data.forstok_order_id || data.id },
                { name: "Channel", value: data.channel },
                { name: "Marketplace ID", value: data.local_id },
                { name: "Ordered At", value: dayjs(data.ordered_at).format("DD MMMM YYYY HH:mm:ss") + " WIB" },
                { name: "Customer Name", value: data.customer_info.name },
                { name: "Last Status", value: data.status },
                { name: "Items Count", value: data.item_lines.length },
                { name: "Error Message", value: errorMessage },
              ],
            },
          ],
        };

        const response = await this.httpService.axiosRef.post(url, cardData);

        try {
          if (typeof response.data === "string" && response.data.includes("error 429")) {
            await wait(2 ** attempt * 1000);
          } else {
            return JSON.parse(response.data);
          }
        } catch (err) {
          console.dir({
            title: "Send Error To MS Teams Failed",
            error: response.data,
          });
        }
      } catch (err) {
        console.log(err);
      }
    }
  }
}
