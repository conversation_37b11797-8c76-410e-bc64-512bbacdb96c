
import { ApiProperty } from "@nestjs/swagger";
import { IsEnum, IsOptional, IsString } from "class-validator";
import { PaginationParamDto } from "src/common/pagination-param.dto";
import { ForstokChannel } from "src/enum/forstok-salesorder-status.enum";

export class FilterForstokOrderErrorDto extends PaginationParamDto {
  @ApiProperty({required:false, description: "order channel", enum: ForstokChannel })
  @IsOptional()
  @IsEnum(ForstokChannel)
  channel?: string;

  @ApiProperty({required:false, description: "Local_name Or LocalID"})
  @IsOptional()
  @IsString()
  keywords?: string;
}