import { ApiProperty } from "@nestjs/swagger";
import { DimensionDto, WholesaleDto } from "./create-product";
import {
  ArrayMinSize,
  IsArray,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  Max,
  Min,
  ValidateIf,
  ValidateNested,
} from "class-validator";
import { Type } from "class-transformer";

export class UpdateProductDto {
  @ApiProperty()
  @IsNotEmpty()
  sku: string;

  @ApiProperty({ required: false })
  @IsOptional()
  description: string;

  @ApiProperty({ required: false })
  @IsOptional()
  name: string;

  @ApiProperty({ required: false })
  @ValidateIf((obj) => obj.stock)
  @IsNumber()
  @Min(0)
  @Max(10000000)
  @Type(() => Number)
  stock: number;

  @ApiProperty({ required: false })
  @ValidateIf((obj) => obj.weight)
  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  @Max(500)
  @Type(() => Number)
  weight: number;

  @ApiProperty({ required: false })
  @ValidateIf((obj) => obj.dimension)
  @IsNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => DimensionDto)
  dimension: DimensionDto;

  @ApiProperty({ required: false })
  @ValidateIf((obj) => obj.currPrice)
  @IsNumber()
  @Type(() => Number)
  @Min(0)
  @Max(100000000)
  currPrice: number;

  @ApiProperty({ required: false })
  @ValidateIf((obj) => obj.oriPrice)
  @IsNumber()
  @Type(() => Number)
  @Min(0)
  @Max(100000000)
  oriPrice: number;

  @ApiProperty({ required: false })
  @ValidateIf((obj) => obj.wholesale)
  @ValidateNested({ each: true })
  @Type(() => WholesaleDto)
  @IsArray()
  @ArrayMinSize(1)
  wholesale: Array<WholesaleDto>;

  @ApiProperty({ isArray: true, maxItems: 5, type: "string", format: "binary", required: false })
  images: Array<Record<string, any>>;

  @ApiProperty({ required: false, type: "string", format: "binary" })
  video: Record<string, any>;

  @ApiProperty({ required: false })
  videoYoutube: string;

  @ApiProperty({ required: false })
  @ValidateIf((obj) => obj.shopeeCategoryId)
  @IsNumber()
  @Type(() => Number)
  shopeeCategoryId: number;

  storeId: string;
  logistic: Array<Record<string, any>>;
  itemId: number;
}
