import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsArray, IsNotEmpty, IsN<PERSON>ber, IsOptional, IsPositive, IsString, Min, ValidateNested } from "class-validator";

class AddItemBody {
  @ApiProperty({ required: false, default: '' })
  @IsString()
  @IsOptional()
  newSku: string;

  @ApiProperty({ required: true})
  @IsString()
  @IsNotEmpty()
  oldSku?: string;

  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ required: true, type: Number, default:0 })
  @IsNumber()
  @IsNotEmpty()
  updateQtyStock: number;

  @ApiProperty({ required: true, type: Number, default:0 })
  @IsNumber()
  @IsNotEmpty()
  qty: number;
}

export class UpdateForstokOrder {
  @ApiProperty({
    type: Array,
    example: [
      {
        newSku: "113830037",
        oldSku: "190150580",
        name: "WILD PINE HAND BALM 30ML",
        updateQtyStock: 0,
        qty: 1
        // untuk update sku dan name
      },
      {
        newSku: "",
        oldSku: "190253174",
        name: "VANILLA PUMPKIN HAND CREAM 30ML",
        updateQtyStock: 100010,
        qty: 2
        //tidak ada update sku dan terdapat update stock pada redis
      }
    ],
  })
  @IsArray()
  @ValidateNested({ each: true, message: "* must be not empty array." })
  @Type(() => AddItemBody)
  data: Array<AddItemBody>;
}