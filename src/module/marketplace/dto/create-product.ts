import { ApiProperty } from "@nestjs/swagger";
import { <PERSON><PERSON>yMinSize, IsArray, IsNotEmpty, IsNumber, Max, Min, ValidateIf, ValidateNested } from "class-validator";
import { Type } from "class-transformer";

export class DimensionDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  @Max(10000)
  @Type(() => Number)
  height: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  @Max(10000)
  @Type(() => Number)
  length: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  @Max(10000)
  @Type(() => Number)
  width: number;
}

export class WholesaleDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  @Max(100000000)
  @Type(() => Number)
  minQty: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  @Max(100000000)
  @Type(() => Number)
  maxQty: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  @Max(100000000)
  @Type(() => Number)
  price: number;
}

export class CreateProductDto {
  @ApiProperty()
  @IsNotEmpty()
  sku: string;

  @ApiProperty()
  @IsNotEmpty()
  description: string;

  @ApiProperty()
  @IsNotEmpty()
  name: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  @Max(10000000)
  @Type(() => Number)
  stock: number;

  @ApiProperty({ required: false })
  @ValidateIf((obj) => !obj.dimension)
  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  @Max(500)
  @Type(() => Number)
  weight: number;

  @ApiProperty({ required: false })
  @ValidateIf((obj) => !obj.weight)
  @IsNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => DimensionDto)
  dimension: DimensionDto;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  @Type(() => Number)
  @Min(0)
  @Max(100000000)
  price: number;

  @ApiProperty({ required: false })
  @ValidateIf((obj) => obj.wholesale)
  @ValidateNested({ each: true })
  @Type(() => WholesaleDto)
  @IsArray()
  @ArrayMinSize(1)
  wholesale: Array<WholesaleDto>;

  @ApiProperty({ isArray: true, maxItems: 5, type: "string", format: "binary" })
  images: Array<Record<string, any>>;

  @ApiProperty({ required: false, type: "string", format: "binary" })
  video: Record<string, any>;

  @ApiProperty({ required: false })
  videoYoutube: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  @Type(() => Number)
  shopeeCategoryId: number;

  storeId: string;
}
