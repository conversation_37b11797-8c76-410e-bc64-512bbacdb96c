import { Redis } from "ioredis";
import { AwsS3Service, IFetchFile } from "../microservices/aws-s3/aws-s3.service";
import { ShopeeProductService } from "./shopee/product/product.service";
import { ConfigService } from "@nestjs/config";
import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { SyncProductPromoImgDto } from "./dto/sync-product-promo-img.dto";
import { ShopeeStoreService } from "./shopee/store/store.service";
import { InjectModel } from "@nestjs/mongoose";
import { ProductImageUpdate, ProductImageUpdateDocument } from "./schema/product-image-update.schema";
import { Model, PaginateModel } from "mongoose";
import { FilterForstokOrderErrorDto } from "./dto/filter-forstock-order-error.dto";
import { ForstokOrderError, ForstokOrderErrorDocument } from "./schema/forstok-order-error.schema";
import { UpdateForstokOrder } from "./dto/update-forstok-order.dto";
import { Callback, CallbackDocument } from "../callback/schema/callback.schema";
import { DEFAULT_REDIS_NAMESPACE, InjectRedis } from "@liaoliaots/nestjs-redis";
import { CallbackService } from "../callback/callback.service";
import { ApiOmsService } from "../microservices/oms/api-oms.service";
import { ForstokOrder, ForstokOrderDocument } from "./schema/forstok-order.schema";
import { PushOrderLog, PushOrderLogDocument } from "./schema/push-order-log.schema";
import { ApiUsersService } from "../microservices/users/api-users.service";

@Injectable()
export class MarketplaceService {
  constructor(
    @InjectModel(ProductImageUpdate.name)
    private readonly productImageUpdateModel: Model<ProductImageUpdateDocument>,
    @InjectModel(ForstokOrderError.name)
    private readonly forStokErrorModel: PaginateModel<ForstokOrderErrorDocument>,
    @InjectModel(Callback.name)
    private readonly callbackModel: Model<CallbackDocument>,
    @InjectModel(ForstokOrder.name)
    private forstokOrderModel: PaginateModel<ForstokOrderDocument>,
    @InjectModel(PushOrderLog.name)
    private pushOrderLogModel: PaginateModel<PushOrderLogDocument>,
    @InjectRedis(DEFAULT_REDIS_NAMESPACE) private readonly redisService: Redis,
    private readonly aws3Service: AwsS3Service,
    private readonly omsService: ApiOmsService,
    private readonly userService: ApiUsersService,
    private readonly callbackService: CallbackService,
    private readonly shopeeProductService: ShopeeProductService,
    private readonly shopeeStoreService: ShopeeStoreService,
    private readonly configService: ConfigService,
  ) {}

  async updateProductPromoImage(payload: SyncProductPromoImgDto) {
    const stores = await this.shopeeStoreService.storeList({ ids: payload.storeIds });

    if (stores.totalDocs !== payload.storeIds.length) {
      throw new HttpException("Terdapat store yang tidak ditemukan", HttpStatus.BAD_REQUEST);
    }

    const folder = this.configService.get<string>("PROMO_PRODUCT_IMG_S3_FOLDER");
    const fileList = await this.aws3Service.getList(folder);

    const list = {};
    const limit = 20;
    const batch = Math.ceil(fileList.length / limit);

    let i = -1;
    while (++i < batch) {
      const data = fileList.splice(0, limit);
      await Promise.all(
        data.map(async (f) => {
          const [_, originalname] = f.Key.split("/");

          if (originalname) {
            const file = await this.aws3Service.fetchFile(f.Key);
            const [sku] = originalname.split(".");
            list[sku] ||= [];
            list[sku].push(file);
          }
        }),
      );
    }

    const productUpdateList = await this.productImageUpdateModel.find({ sku: Object.keys(list) }).lean();
    const updateRecordHash = {};
    productUpdateList.map((p) => (updateRecordHash[p.sku] = p));

    const now = new Date();
    for (const store of stores.docs) {
      await Promise.all(
        Object.keys(list).map(async (key) => {
          const updateRecord = this._getUpdateRecordData(updateRecordHash[key], now, store._id.toString());

          const files = this._filterImageDate(list[key], updateRecord.data[updateRecord.lastIdx]?.lastUpdate);

          if (!files.length) return;

          const update = await this.shopeeProductService.updateImageOnly(key, store._id.toString(), files);

          let record = [
            {
              storeId: store._id,
              oldImages: update.oldImages,
              newImages: update.newImages,
              lastUpdate: now,
            },
          ];

          if (updateRecord.indexes.length === 5) {
            updateRecordHash[key]?.record.splice(updateRecord.indexes[updateRecord.lastIdx], 1);
            record = [...updateRecordHash[key]?.record, ...record];
          }

          await this.productImageUpdateModel.updateOne(
            { productId: update.productId },
            { productId: update.productId, sku: key, record },
            { upsert: true },
          );
        }),
      );
    }

    return `Successfully update promo image for ${stores.docs.map((s) => `${s.storeName} (${s.platform})`).toString()}`;
  }

  private _filterImageDate(images: Array<IFetchFile>, comparator: Date) {
    if (!comparator) return images;

    return images.filter((img) => img.date > comparator);
  }

  private _getUpdateRecordData(recordData: ProductImageUpdate, comparator: Date, storeId: string) {
    const records = { indexes: [], data: [] };

    for (const [i, r] of Object.entries(recordData?.record || [])) {
      if (r.storeId.toString() === storeId) {
        records.indexes.push(i);
        records.data.push(r);
      }
    }

    if (records.data[records.data.length - 1]?.lastUpdate > comparator) return;

    return { ...records, lastIdx: records.indexes.length - 1 };
  }

  async getForstokOrderError(query: FilterForstokOrderErrorDto) {
    try {
      const { keywords, channel } = query;

      const filter = {};

      if (channel) filter["storeName"] = channel.replace("_", " ");

      if (keywords)
        filter["$or"] = [
          { localId: { $regex: new RegExp(keywords, "i") } },
          { localName: { $regex: new RegExp(keywords, "i") } },
        ];

      const page = query.page || 1;
      const limit = query.limit || 10;
      const options = {
        page: Number(page),
        limit: Number(limit),
        sort: query.sort,
      };

      return await this.forStokErrorModel.paginate(filter, options);
    } catch (error) {
      throw error;
    }
  }

  async getForstockDetail(id: string) {
    return await this.forStokErrorModel.findOne({ _id: id });
  }

  async pushForstokOrder(forstokId: number, payload: UpdateForstokOrder, req: any) {
    try {
      const forstokError = await this.forStokErrorModel.findOne({ forstokOrderId: forstokId }).lean();
      const itemSubtitute = [];
      const skus = [];
      const updateStockRedis = [];
      for (let index = 0; index < payload.data.length; index++) {
        skus.push(payload.data[index].newSku);
      }

      const getProduct = await this.omsService.getProductDetails(skus);
      for (let index = 0; index < payload.data.length; index++) {

        if (payload.data[index].newSku !== "") {
          const item = forstokError.itemLines.find((item) => item.sku === payload.data[index].oldSku);

          if (item) {
            item["sku"] = payload.data[index].newSku;
            item["name"] = payload.data[index].name;
            item["variantName"] = payload.data[index].name;
            item["variantSku"] = payload.data[index].newSku;
            item["qty"] = payload.data[index].qty;
  
            itemSubtitute.push(payload.data[index].newSku);
            if (payload.data[index].updateQtyStock !== 0) {
              updateStockRedis.push({ sku: payload.data[index].newSku, qty: payload.data[index].updateQtyStock });
            }
          }
        }

        if (
          payload.data[index].oldSku == "" &&
          forstokError.itemLines.find((item) => item.sku === payload.data[index].newSku) == null
        ) {
          const newProduct = getProduct.find((item) => item.sku === payload.data[index].newSku);
          const product = {
            id: newProduct.base_article_id,
            localId: newProduct.row_id,
            sku: newProduct.sku,
            name: newProduct.name,
            variantName: newProduct.name,
            variantId: newProduct.variant_id,
            variantSku: newProduct.sku,
            price: newProduct.price,
            salePrice: newProduct.price,
            totalPrice: newProduct.price,
            voucherAmount: 0,
            voucherCode: null,
            voucherSeller: 0,
            taxPrice: 0,
            fulfillByChannel: false,
            shippingProvider: forstokError.itemLines[0].shippingProvider,
            shippingProviderType: forstokError.itemLines[0].shippingProviderType,
            trackingNumber: forstokError.itemLines[0].trackingNumber,
            note: null,
            internalNote: null,
            qty: payload.data[index].qty,
          };
          forstokError.itemLines.push(product);
          itemSubtitute.push(product.sku);
          if (payload.data[index].updateQtyStock !== 0) {
            updateStockRedis.push({ sku: payload.data[index].newSku, qty: payload.data[index].updateQtyStock });
          }
        }

        if (payload.data[index].updateQtyStock !== 0) {
          updateStockRedis.push({ sku: payload.data[index].oldSku, qty: payload.data[index].updateQtyStock });
        }
      }

      await this.forStokErrorModel.deleteOne({ forstokOrderId: forstokId });
      await this.forstokOrderModel.deleteOne({ forstokOrderId: forstokId });
      const user = await this.userService.getProfileAdmin(req);

      await this.pushOrderLogModel.create({
        name: user.data.firstName,
        forstokOrderId: forstokError.forstokOrderId,
        keycloakUserId: user.data.keycloakUserId,
        email: user.data.email,
        itemSubtitute: [...new Set(itemSubtitute)],
      });

      Object.assign(forstokError, { id: forstokId });

      for (let index = 0; index < updateStockRedis.length; index++) {
        const key = `stock:${updateStockRedis[index].sku}-34999-stock`;
        await this.redisService.set(key, updateStockRedis[index].qty);
      }

      return this.callbackService.forcePushOrder(forstokError);
    } catch (error) {
      throw error;
    }
  }
}
