import { InjectModel } from "@nestjs/mongoose";
import { PaginateModel } from "mongoose";
import { HttpException, HttpStatus } from "@nestjs/common";
import { isMongoId } from "class-validator";
import { MarketplaceStoreList, MarketplaceStoreListDocument } from "../../schema/store-list.schema";
import { StoreRegisterMarketplaceDto } from "./dto/shopee-store-register.dto";
import { ShopeeStoreListDto } from "./dto/shopee-store-list.dto";
import { ShopeeStoreUpdateDto } from "./dto/shopee-store-update.dto";
import { ApiUtilsService } from "../../../microservices/utils/api-utils.service";
import { IShopeeStoreUpdate, ShopeeApiStoreService } from "../../../microservices/shopee";

export class ShopeeStoreService {
  constructor(
    @InjectModel(MarketplaceStoreList.name)
    private readonly marketplaceStoreListModel: PaginateModel<MarketplaceStoreListDocument>,
    private readonly utilService: ApiUtilsService,
    private readonly shopeeStoreService: ShopeeApiStoreService,
  ) {}

  async storeRegister(payload: StoreRegisterMarketplaceDto) {
    try {
      return await this.marketplaceStoreListModel.create(payload);
    } catch (err) {
      if (err?.code === 11000) {
        throw new HttpException("Store sudah terdaftar", HttpStatus.BAD_REQUEST);
      }
      throw err;
    }
  }

  async storeList(query: ShopeeStoreListDto) {
    const filter: Record<string, any> = { deletedAt: null };

    if (query.platform) filter.platform = query.platform;
    if (query.storeName) filter.storeName = new RegExp(query.storeName, "i");
    if (query.ids) filter._id = query.ids;

    return await this.marketplaceStoreListModel.paginate(filter, {
      page: query.page || 1,
      limit: query.limit || 10,
      sort: query.sort,
      forceCountFn: true,
    });
  }

  async storeDetail(id: string) {
    if (!isMongoId(id)) {
      throw new HttpException("Id harus berupa mongoID", HttpStatus.BAD_REQUEST);
    }

    const data = await this.marketplaceStoreListModel.findById(id).exec();
    if (!data) {
      throw new HttpException("Store tidak ditemukan", HttpStatus.BAD_REQUEST);
    }

    return data;
  }

  async storeUpdate(id: string, payload: ShopeeStoreUpdateDto) {
    if (!isMongoId(id)) {
      throw new HttpException("Id harus berupa mongoID", HttpStatus.BAD_REQUEST);
    }

    const store = await this.marketplaceStoreListModel.findById(id);

    if (!store) {
      throw new HttpException("Store tidak ditemukan", HttpStatus.BAD_REQUEST);
    }

    const mock: IShopeeStoreUpdate = {};

    if (payload.storeName) {
      mock.shop_name = payload.storeName;
      store.storeName = payload.storeName;
    }
    if (payload.description) {
      mock.description = payload.description;
      store.description = payload.description;
    }

    await this.shopeeStoreService.update(store.storeId, mock);
    return await store.save();
  }

  async storeDelete(id: string) {
    if (!isMongoId(id)) {
      throw new HttpException("Id harus berupa mongoID", HttpStatus.BAD_REQUEST);
    }

    return this.marketplaceStoreListModel.findByIdAndUpdate(id, { deletedAt: new Date() });
  }
}
