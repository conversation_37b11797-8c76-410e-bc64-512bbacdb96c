import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UploadedFile,
  UseInterceptors,
} from "@nestjs/common";
import { Resource, RoleMatchingMode, Roles, Scopes } from "keycloak-connect-tbs";
import { <PERSON><PERSON>, Scope } from "../../../../enum/rbac.enum";
import { ApiConsumes, ApiTags } from "@nestjs/swagger";
import { FileInterceptor } from "@nestjs/platform-express";
import { ShopeeStoreService } from "./store.service";
import { Role } from "../../../../enum/Role.enum";
import { StoreRegisterMarketplaceDto } from "./dto/shopee-store-register.dto";
import { ShopeeStoreListDto } from "./dto/shopee-store-list.dto";
import { ShopeeStoreUpdateDto } from "./dto/shopee-store-update.dto";

@ApiTags("Shopee Store")
@Controller("shopee-store")
@Resource(Controllers.SHOPEE_STORE)
@Roles({
  roles: [Role.AdminEcom, `realm:app-${Role.AdminEcom}`, Role.Admin, `realm:app-${Role.Admin}`],
  mode: RoleMatchingMode.ANY,
})
export class ShopeeStoreController {
  constructor(private readonly shopeeStoreService: ShopeeStoreService) {}

  @Post()
  @Scopes(Scope.POST)
  async storeRegister(@Body() body: StoreRegisterMarketplaceDto) {
    return await this.shopeeStoreService.storeRegister(body);
  }

  @Get()
  @Scopes(Scope.GET)
  async storeList(@Query() query: ShopeeStoreListDto) {
    return await this.shopeeStoreService.storeList(query);
  }

  @Get(":id")
  @Scopes(Scope.GET)
  async storeDetail(@Param("id") id: string) {
    return await this.shopeeStoreService.storeDetail(id);
  }

  @ApiConsumes("multipart/form-data")
  @UseInterceptors(FileInterceptor("storeLogo"))
  @Patch(":id")
  @Scopes(Scope.PATCH)
  async storeUpdate(@Param("id") id: string, @Body() body: ShopeeStoreUpdateDto, @UploadedFile() storeLogo) {
    body.storeLogo = storeLogo;
    return await this.shopeeStoreService.storeUpdate(id, body);
  }

  @Delete(":id")
  @Scopes(Scope.DELETE)
  async storeDelete(@Param("id") id: string) {
    return await this.shopeeStoreService.storeDelete(id);
  }
}
