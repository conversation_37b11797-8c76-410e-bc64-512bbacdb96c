import { ApiProperty } from "@nestjs/swagger";
import { MarketplacePlatformEnum } from "../../../../../enum/marketplace.enum";
import { IsEnum, IsNotEmpty } from "class-validator";

export class StoreRegisterMarketplaceDto {
  @ApiProperty({ enum: MarketplacePlatformEnum })
  @IsNotEmpty()
  @IsEnum(MarketplacePlatformEnum)
  platform: MarketplacePlatformEnum;

  @ApiProperty()
  @IsNotEmpty()
  storeId: string;
}
