import { ApiProperty } from "@nestjs/swagger";
import { IsEnum, IsOptional, ValidateIf } from "class-validator";
import { MarketplacePlatformEnum } from "../../../../../enum/marketplace.enum";
import { PaginationParamDto } from "../../../../../common/pagination-param.dto";

export class ShopeeStoreListDto extends PaginationParamDto {
  @ApiProperty({ required: false })
  @ValidateIf((obj) => obj.platform)
  @IsEnum(MarketplacePlatformEnum)
  platform?: MarketplacePlatformEnum;

  @ApiProperty({ required: false })
  @IsOptional()
  storeName?: string;

  ids?: Array<string>;
}
