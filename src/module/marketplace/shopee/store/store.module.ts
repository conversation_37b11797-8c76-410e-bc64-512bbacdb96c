import { Modu<PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { MarketplaceStoreList, MarketplaceStoreListSchema } from "../../schema/store-list.schema";
import { ShopeeStoreController } from "./store.controller";
import { ShopeeStoreService } from "./store.service";
import { ShopeeApiModule } from "../../../microservices/shopee/shopee.module";
import { ApiUtilsModule } from "../../../microservices/utils/api-utils.module";

@Module({
  controllers: [ShopeeStoreController],
  providers: [ShopeeStoreService],
  imports: [
    MongooseModule.forFeature([
      {
        name: MarketplaceStoreList.name,
        schema: MarketplaceStoreListSchema,
      },
    ]),
    ShopeeApiModule,
    ApiUtilsModule,
  ],
  exports: [ShopeeStoreService],
})
export class ShopeeStoreModule {}
