import { Modu<PERSON> } from "@nestjs/common";
import { ShopeeAuthModule } from "./auth/auth.module";
import { ShopeeStoreModule } from "./store/store.module";
import { ShopeeCategoryModule } from "./category/category.module";
import { ShopeeLogisticModule } from "./logistic/logistic.module";
import { ShopeeProductModule } from "./product/product.module";

@Module({
  controllers: [],
  providers: [],
  exports: [],
  imports: [ShopeeAuthModule, ShopeeStoreModule, ShopeeCategoryModule, ShopeeLogisticModule, ShopeeProductModule],
})
export class ShopeeModule {}
