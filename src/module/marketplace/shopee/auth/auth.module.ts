import { Modu<PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { ShopeeAuthController } from "./auth.controller";
import { ShopeeAuthService } from "./auth.service";
import { MarketplaceAuth, MarketplaceAuthSchema } from "../../schema/auth.schema";
import { MarketplaceStoreList, MarketplaceStoreListSchema } from "../../schema/store-list.schema";
import { ShopeeApiModule } from "../../../microservices/shopee/shopee.module";

@Module({
  controllers: [ShopeeAuthController],
  providers: [ShopeeAuthService],
  imports: [
    MongooseModule.forFeature([
      {
        name: MarketplaceAuth.name,
        schema: MarketplaceAuthSchema,
      },
      {
        name: MarketplaceStoreList.name,
        schema: MarketplaceStoreListSchema,
      },
    ]),
    ShopeeApiModule,
  ],
})
export class ShopeeAuthModule {}
