import { Controller, Get, Post, Query } from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import { Public, Resource, RoleMatchingMode, Roles, Scopes } from "keycloak-connect-tbs";
import { <PERSON>s, Scope } from "../../../../enum/rbac.enum";
import { Role } from "../../../../enum/Role.enum";
import { ShopeeAuthService } from "./auth.service";

@ApiTags("Shopee Auth")
@Controller("shopee-auth")
@Resource(Controllers.SHOPEE_AUTH)
@Roles({
  roles: [Role.AdminEcom, `realm:app-${Role.AdminEcom}`, Role.Admin, `realm:app-${Role.Admin}`],
  mode: RoleMatchingMode.ANY,
})
export class ShopeeAuthController {
  constructor(private readonly shopeeAuthService: ShopeeAuthService) {}

  @Post("authorize")
  @Scopes(Scope.POST)
  async storeAuthorize() {
    return await this.shopeeAuthService.generateAuthorization();
  }

  @Get("authorize")
  @Public()
  async authorize(@Query() query: Record<string, any>) {
    return await this.shopeeAuthService.setAccessCode(query.shop_id, query.code, query.main_account_id);
  }
}
