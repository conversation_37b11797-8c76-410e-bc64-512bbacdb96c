import { InjectModel } from "@nestjs/mongoose";
import { Model, PaginateModel } from "mongoose";
import { MarketplaceStoreList, MarketplaceStoreListDocument } from "../../schema/store-list.schema";
import { MarketplaceAuth, MarketplaceAuthDocument } from "../../schema/auth.schema";
import { MarketplacePlatformEnum } from "../../../../enum/marketplace.enum";
import { ShopeeApiAuthService, ShopeeApiStoreService } from "../../../microservices/shopee";

export class ShopeeAuthService {
  constructor(
    @InjectModel(MarketplaceStoreList.name)
    private readonly marketplaceStoreListModel: PaginateModel<MarketplaceStoreListDocument>,
    @InjectModel(MarketplaceAuth.name)
    private readonly marketplaceAuthModel: Model<MarketplaceAuthDocument>,
    private readonly shopeeAuthService: ShopeeApiAuthService,
    private readonly shopeeStoreService: ShopeeApiStoreService,
  ) {}

  async setAccessCode(shop_id: string, code: string, main_account_id: string) {
    const getToken = await this.shopeeAuthService.genAccessToken(+shop_id, code, +main_account_id);
    const store = await this.shopeeStoreService.getDetail(getToken.store_id, getToken.access_token);

    await this.marketplaceAuthModel.updateOne(
      { platform: MarketplacePlatformEnum.shopee, storeId: getToken.store_id },
      {
        accessToken: getToken.access_token,
        accessTokenExp: getToken.access_token_exp,
        refreshToken: getToken.refresh_token,
        refreshTokenExp: getToken.refresh_token_exp,
      },
      { upsert: true },
    );

    return this.marketplaceStoreListModel.findOneAndUpdate(
      { platform: MarketplacePlatformEnum.shopee, storeId: getToken.store_id },
      {
        storeId: getToken.store_id,
        storeName: store.shop_name,
        storeLogo: store.shop_logo,
        description: store.description,
      },
      { new: true, upsert: true },
    );
  }

  async generateAuthorization() {
    return await this.shopeeAuthService.genAuthorization();
  }
}
