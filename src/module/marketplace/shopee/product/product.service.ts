import { InjectModel } from "@nestjs/mongoose";
import { MarketplaceProduct, MarketplaceProductDocument } from "../../schema/product.schema";
import { PaginateModel } from "mongoose";
import { CreateProductDto } from "../../dto/create-product";
import { combineTwoObject, convertByteString, validateImage } from "../../../../utils/function.util";
import { ApiUtilsService } from "../../../microservices/utils/api-utils.service";
import { ShopeeApiProductService, ShopeeApiUtilService } from "../../../microservices/shopee";
import { ShopeeLogisticService } from "../logistic/logistic.service";
import { ShopeeStoreService } from "../store/store.service";
import { MarketplacePlatformEnum } from "../../../../enum/marketplace.enum";
import { UpdateProductDto } from "../../dto/update-product";
import { HttpException, HttpStatus } from "@nestjs/common";

export class ShopeeProductService {
  constructor(
    @InjectModel(MarketplaceProduct.name)
    private readonly marketplaceProductModel: PaginateModel<MarketplaceProductDocument>,
    private readonly utilService: ApiUtilsService,
    private readonly shopeeUtilService: ShopeeApiUtilService,
    private readonly shopeeProductService: ShopeeApiProductService,
    private readonly storeService: ShopeeStoreService,
    private readonly logisticService: ShopeeLogisticService,
  ) {}

  async create(payload: CreateProductDto) {
    const store = await this.storeService.storeDetail(payload.storeId);

    if (!payload.images?.length) {
      throw new HttpException("Image minimal 1", HttpStatus.BAD_REQUEST);
    }

    const images = [];
    for (const img of payload.images) {
      const maxSizeImage = await this.utilService.getConfig("mp.image-max-size", true);
      const maxSize = convertByteString(maxSizeImage || "1 mb", "kb");
      await validateImage(img, maxSize);
      const upload = await this.shopeeUtilService.uploadImage(img.buffer, img.originalname);

      images.push(upload);
    }
    payload.images = images;

    const logisticData = await this.logisticService.getList({ enabled: true, storeId: store._id });

    const create = await this.shopeeProductService.create(store.storeId, logisticData.docs, payload);

    const mock = {
      sku: payload.sku,
      name: payload.name,
      description: payload.description,
      currPrice: payload.price,
      oriPrice: payload.price,
      stock: payload.stock,
      weight: payload.weight,
      wholesale: payload.wholesale,
      dimension: payload.dimension || {},
      isPreOrder: false,
      brandName: "",
      $addToSet: {
        images: {
          platform: MarketplacePlatformEnum.shopee,
          storeId: store._id,
          data: {
            ids: create.images.image_id_list,
            urls: create.images.image_url_list,
          },
        },
        marketplaceIds: {
          platform: MarketplacePlatformEnum.shopee,
          id: create.item_id,
          storeId: store._id,
        },
        categories: {
          platform: MarketplacePlatformEnum.shopee,
          id: payload.shopeeCategoryId,
          storeId: store._id,
        },
      },
    };

    return await this.marketplaceProductModel
      .findOneAndUpdate({ sku: payload.sku }, mock, { new: true, upsert: true })
      .exec();
  }

  async sync(storeId: string) {
    const store = await this.storeService.storeDetail(storeId);

    let hasData = true;
    let page = 1;
    while (hasData) {
      const data = await this.shopeeProductService.getList(store.storeId, page++);

      const productDetail = await this.shopeeProductService.detail(
        store.storeId,
        data.item.map((i) => i.item_id),
      );

      await Promise.all(
        productDetail.map(async (p) => {
          const mock = this.normalizeProduct(p, store._id);
          await this.marketplaceProductModel.findOneAndUpdate({ sku: mock.sku }, mock, { upsert: true });
        }),
      );

      if (!data.has_next_page) {
        hasData = false;
        break;
      }
    }
    return "Successfully sync product";
  }

  async update(storeId: string, payload: UpdateProductDto) {
    const store = await this.storeService.storeDetail(storeId);
    const product = await this.marketplaceProductModel.findOne({ sku: payload.sku }).lean();
    const logisticData = await this.logisticService.getList({ enabled: true, storeId: store._id });

    if (!product) {
      throw new HttpException(`Product dengan sku ${payload.sku} tidak ditemukan`, HttpStatus.BAD_REQUEST);
    }

    payload.logistic = logisticData.docs;
    const mock = combineTwoObject(product, payload, 2);
    const now = Date.now();

    const images = [];
    for (const img of payload.images) {
      const maxSizeImage = await this.utilService.getConfig("mp.image-max-size", true);
      const maxSize = convertByteString(maxSizeImage || "1 mb", "kb");
      await validateImage(img, maxSize);
      const upload = await this.shopeeUtilService.uploadImage(img.buffer, now + "-" + img.originalname);

      images.push(upload);
    }
    payload.images = images;

    for (const i of mock.images) {
      if (i.platform === MarketplacePlatformEnum.shopee) {
        for (const [idx, id] of Object.entries(i.data.ids)) {
          if (Number(idx) > payload.images.length - 1) {
            payload.images.push({ id, url: i.data.urls[idx] });
          }
        }
        break;
      }
    }

    mock.images = payload.images;
    mock.images.splice(9);
    mock.itemId = product.marketplaceIds.filter((a) => a.platform === MarketplacePlatformEnum.shopee)[0].id;
    mock.shopeeCategoryId = product.categories.filter((a) => a.platform === MarketplacePlatformEnum.shopee)[0].id;

    const update = await this.shopeeProductService.update(store.storeId, mock);

    const mockDB = {
      sku: mock.sku,
      name: mock.name,
      description: mock.description,
      currPrice: mock.currPrice,
      oriPrice: mock.oriPrice,
      stock: mock.stock,
      weight: mock.weight,
      wholesale: mock.wholesale,
      dimension: mock.dimension || {},
      isPreOrder: false,
      brandName: "",
      $set: {
        images: {
          $concatArrays: [
            {
              $filter: {
                input: "$image",
                as: "img",
                cond: { $ne: ["$$img.storeId", store._id] },
              },
            },
            [
              {
                platform: MarketplacePlatformEnum.shopee,
                storeId: store._id,
                data: {
                  ids: update.images.image_id_list,
                  urls: update.images.image_url_list,
                },
              },
            ],
          ],
        },
        marketplaceIds: {
          $concatArrays: [
            {
              $filter: {
                input: "$marketplaceIds",
                as: "mpId",
                cond: { $ne: ["$$mpId.storeId", store._id] },
              },
            },
            [
              {
                platform: MarketplacePlatformEnum.shopee,
                id: update.item_id,
                storeId: store._id,
              },
            ],
          ],
        },
        categories: {
          $concatArrays: [
            {
              $filter: {
                input: "$categories",
                as: "cat",
                cond: { $ne: ["$$cat.storeId", store._id] },
              },
            },
            [
              {
                platform: MarketplacePlatformEnum.shopee,
                id: mock.shopeeCategoryId,
                storeId: store._id,
              },
            ],
          ],
        },
      },
    };

    return await this.marketplaceProductModel
      .findOneAndUpdate({ sku: mock.sku }, mockDB, { new: true, upsert: true })
      .exec();
  }

  async updateImageOnly(sku: string, storeId: string, images: Array<Record<string, any>>) {
    const store = await this.storeService.storeDetail(storeId);
    const product = await this.marketplaceProductModel.findOne({ sku, "marketplaceIds.store": store._id }).lean();

    if (!product) {
      throw new HttpException(`Product dengan sku ${sku} tidak ditemukan`, HttpStatus.BAD_REQUEST);
    }

    const chosen = product.images.filter((a) => a.storeId.toString() === store._id.toString())[0].data;
    const newImages = JSON.parse(JSON.stringify(chosen));
    const oldImages = { ids: [], urls: [] };
    const changedImages = { ids: [], urls: [] };
    const now = Date.now();
    for (const [i, img] of Object.entries(images)) {
      const maxSizeImage = await this.utilService.getConfig("mp.image-max-size", true);
      const maxSize = convertByteString(maxSizeImage || "1 mb", "kb");
      await validateImage(img, maxSize);

      const upload = await this.shopeeUtilService.uploadImage(img.buffer, now + "-" + img.originalname);
      newImages.ids[i] = upload.id;
      newImages.urls[i] = upload.url;

      oldImages.ids.push(chosen.ids[i]);
      oldImages.urls.push(chosen.urls[i]);
      changedImages.ids.push(upload.id);
      changedImages.urls.push(upload.url);
    }

    const itemId = product.marketplaceIds.filter((a) => a.storeId.toString() === store._id.toString())[0]?.id;
    await this.shopeeProductService.updateImageOnly(store.storeId, itemId, newImages.ids);

    return { oldImages, itemId, newImages: changedImages, productId: product._id };
  }

  private normalizeProduct(product: Record<string, any>, storeId: string) {
    return {
      sku: product.item_sku,
      name: product.item_name,
      brandName: product.brand.original_brand_name,
      $addToSet: {
        images: {
          platform: MarketplacePlatformEnum.shopee,
          data: {
            ids: product.image.image_id_list,
            urls: product.image.image_url_list,
          },
          storeId,
        },
        marketplaceIds: {
          platform: MarketplacePlatformEnum.shopee,
          id: product.item_id,
          storeId,
        },
        categories: {
          platform: MarketplacePlatformEnum.shopee,
          id: product.category_id,
          storeId,
        },
      },
      dimension: {
        length: product.dimension.package_length,
        height: product.dimension.package_height,
        width: product.dimension.package_width,
      },
      stock: product.stock_info_v2?.summary_info?.total_available_stock || 0,
      description:
        product.description ||
        product.description_info?.extended_description?.field_list?.map((i) => i.text).join("\n"),
      weight: product.weight,
      wholesale: product.wholesales?.map((w) => {
        return {
          maxQty: w.max_count,
          minQty: w.min_count,
          price: w.unit_price,
        };
      }),
      oriPrice: product.price_info?.[0]?.original_price || 0,
      currPrice: product.price_info?.[0]?.current_price || 0,
      isPreOrder: product.pre_order.is_pre_order,
      videos: [],
    };
  }
}
