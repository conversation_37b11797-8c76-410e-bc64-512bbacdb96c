import { Modu<PERSON> } from "@nestjs/common";
import { ShopeeProductController } from "./product.controller";
import { ShopeeProductService } from "./product.service";
import { MongooseModule } from "@nestjs/mongoose";
import { MarketplaceProduct, MarketplaceProductSchema } from "../../schema/product.schema";
import { ApiUtilsModule } from "../../../microservices/utils/api-utils.module";
import { ShopeeLogisticModule } from "../logistic/logistic.module";
import { ShopeeApiModule } from "../../../microservices/shopee/shopee.module";
import { ShopeeStoreModule } from "../store/store.module";

@Module({
  controllers: [ShopeeProductController],
  providers: [ShopeeProductService],
  imports: [
    MongooseModule.forFeature([
      {
        name: MarketplaceProduct.name,
        schema: MarketplaceProductSchema,
      },
    ]),
    ApiUtilsModule,
    ShopeeApiModule,
    ShopeeLogisticModule,
    ShopeeStoreModule,
  ],
  exports: [ShopeeProductService],
})
export class ShopeeProductModule {}
