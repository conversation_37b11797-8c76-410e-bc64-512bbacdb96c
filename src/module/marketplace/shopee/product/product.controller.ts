import { ApiConsumes, ApiTags } from "@nestjs/swagger";
import { Body, Controller, Get, Param, Patch, Post, UploadedFiles, UseInterceptors } from "@nestjs/common";
import { Resource, RoleMatchingMode, Roles, Scopes } from "keycloak-connect-tbs";
import { <PERSON>s, Scope } from "../../../../enum/rbac.enum";
import { Role } from "../../../../enum/Role.enum";
import { ShopeeProductService } from "./product.service";
import { CreateProductDto } from "../../dto/create-product";
import { FilesInterceptor } from "@nestjs/platform-express";
import { UpdateProductDto } from "../../dto/update-product";

@ApiTags("Shopee Product")
@Controller("shopee-product")
@Resource(Controllers.SHOPEE_PRODUCT)
@Roles({
  roles: [Role.AdminEcom, `realm:app-${Role.AdminEcom}`, Role.Admin, `realm:app-${Role.Admin}`],
  mode: RoleMatchingMode.ANY,
})
export class ShopeeProductController {
  constructor(private readonly shopeeProductService: ShopeeProductService) {}

  @ApiConsumes("multipart/form-data")
  @UseInterceptors(FilesInterceptor("images", 5))
  @Post(":storeId")
  @Scopes(Scope.GET)
  async create(@Param("storeId") storeId: string, @Body() body: CreateProductDto, @UploadedFiles() images) {
    body.storeId = storeId;
    body.images = images;
    return await this.shopeeProductService.create(body);
  }

  @Get("sync/:storeId")
  @Scopes(Scope.GET)
  async getList(@Param("storeId") storeId: string) {
    return await this.shopeeProductService.sync(storeId);
  }

  @ApiConsumes("multipart/form-data")
  @UseInterceptors(FilesInterceptor("images", 5))
  @Patch(":storeId")
  @Scopes(Scope.PATCH)
  async update(@Body() body: UpdateProductDto, @Param("storeId") storeId: string, @UploadedFiles() images) {
    body.images = images;
    return await this.shopeeProductService.update(storeId, body);
  }
}
