import { <PERSON><PERSON>, Schem<PERSON>, SchemaFactory } from "@nestjs/mongoose";
import mongoose, { Document } from "mongoose";
import * as mongoosePaginate from "mongoose-paginate-v2";
import { MarketplaceStoreList } from "../../../schema/store-list.schema";

@Schema({ timestamps: true })
export class ShopeeCategory {
  @Prop({ index: true })
  category_id: number;

  @Prop({ index: true })
  parentId: number;

  @Prop()
  name: string;

  @Prop()
  has_child: boolean;

  @Prop({ index: true })
  level: number;

  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: MarketplaceStoreList.name,
  })
  storeId: string;
}

export type ShopeeCategoryDocument = ShopeeCategory & Document;

export const ShopeeCategorySchema = SchemaFactory.createForClass(ShopeeCategory);
ShopeeCategorySchema.plugin(mongoosePaginate);
