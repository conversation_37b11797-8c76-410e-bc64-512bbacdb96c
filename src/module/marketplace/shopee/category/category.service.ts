import { InjectModel } from "@nestjs/mongoose";
import { ShopeeCategory, ShopeeCategoryDocument } from "./schema/category.schema";
import { ShopeeApiCategoryService } from "../../../microservices/shopee";
import { PaginateModel } from "mongoose";
import { ShopeeStoreService } from "../store/store.service";
import { CategoryListDto } from "./dto/category-list.dto";

export class ShopeeCategoryService {
  constructor(
    @InjectModel(ShopeeCategory.name)
    private readonly categoryModel: PaginateModel<ShopeeCategoryDocument>,
    private readonly shopeeCategoryService: ShopeeApiCategoryService,
    private readonly storeService: ShopeeStoreService,
  ) {}

  async syncList(storeId: string) {
    const store = await this.storeService.storeDetail(storeId);
    const category = await this.shopeeCategoryService.getList(store.storeId);

    const hashCategory = {};
    const mock: Record<string, any> = {};
    category.map((c) => {
      if (!c.parent_category_id) {
        mock[c.category_id] = {
          category_id: c.category_id,
          parentId: null,
          name: c.original_category_name,
          has_child: c.has_children,
          level: 1,
          storeId: store._id,
        };
      } else {
        hashCategory[c.category_id] = c;
      }
    });

    let hasCategory = true;
    while (hasCategory) {
      const arr = Object.values(hashCategory);
      if (!arr.length) {
        hasCategory = false;
        break;
      }

      for (const item of arr) {
        if (mock[item["parent_category_id"]]) {
          mock[item["category_id"]] = {
            category_id: item["category_id"],
            parentId: item["parent_category_id"],
            name: item["original_category_name"],
            has_child: item["has_children"],
            level: mock[item["parent_category_id"]].level + 1,
            storeId: store._id,
          };
          delete hashCategory[item["category_id"]];
        }
      }
    }

    await this.categoryModel.insertMany(Object.values(mock));

    return "Successfully sync category list";
  }

  async getList(payload: CategoryListDto) {
    const filter: Record<string, any> = {};

    if (payload.name) filter.name = new RegExp(payload.name, "i");
    if (payload.parentId) filter.parentId = payload.parentId;
    else filter.level = 1;

    return await this.categoryModel.paginate(filter, {
      page: payload.page || 1,
      limit: payload.limit || 10,
      lean: true,
      forceCountFn: true,
    });
  }
}
