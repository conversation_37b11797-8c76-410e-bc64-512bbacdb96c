import { Modu<PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { ShopeeCategoryController } from "./category.controller";
import { ShopeeCategoryService } from "./category.service";
import { ShopeeCategory, ShopeeCategorySchema } from "./schema/category.schema";
import { ShopeeApiModule } from "../../../microservices/shopee/shopee.module";
import { ApiUtilsModule } from "../../../microservices/utils/api-utils.module";
import { ShopeeStoreModule } from "../store/store.module";

@Module({
  controllers: [ShopeeCategoryController],
  providers: [ShopeeCategoryService],
  imports: [
    MongooseModule.forFeature([
      {
        name: ShopeeCategory.name,
        schema: ShopeeCategorySchema,
      },
    ]),
    ShopeeApiModule,
    ApiUtilsModule,
    ShopeeStoreModule,
  ],
})
export class ShopeeCategoryModule {}
