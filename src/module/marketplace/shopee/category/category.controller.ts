import { ApiTags } from "@nestjs/swagger";
import { Controller, Get, Param, Query } from "@nestjs/common";
import { Resource, RoleMatchingMode, Roles, Scopes } from "keycloak-connect-tbs";
import { <PERSON><PERSON>, Scope } from "../../../../enum/rbac.enum";
import { Role } from "../../../../enum/Role.enum";
import { CategoryListDto } from "./dto/category-list.dto";
import { ShopeeCategoryService } from "./category.service";

@ApiTags("Shopee Product Category")
@Controller("shopee-category")
@Resource(Controllers.SHOPEE_CATEGORY)
@Roles({
  roles: [Role.AdminEcom, `realm:app-${Role.AdminEcom}`, Role.Admin, `realm:app-${Role.Admin}`],
  mode: RoleMatchingMode.ANY,
})
export class ShopeeCategoryController {
  constructor(private readonly shopeeCategoryService: ShopeeCategoryService) {}

  @Get("sync-list/:storeId")
  @Scopes(Scope.GET)
  async syncList(@Param("storeId") storeId: string) {
    return await this.shopeeCategoryService.syncList(storeId);
  }

  @Get()
  @Scopes(Scope.GET)
  async getList(@Query() query: CategoryListDto) {
    return await this.shopeeCategoryService.getList(query);
  }
}
