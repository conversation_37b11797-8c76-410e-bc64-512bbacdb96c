import { ApiTags } from "@nestjs/swagger";
import { Controller, Get, Param, Query } from "@nestjs/common";
import { Resource, RoleMatchingMode, Roles, Scopes } from "keycloak-connect-tbs";
import { <PERSON><PERSON>, Scope } from "../../../../enum/rbac.enum";
import { Role } from "../../../../enum/Role.enum";
import { ShopeeLogisticService } from "./logistic.service";
import { LogisticListDto } from "./dto/logistic-list.dto";

@ApiTags("Shopee Logistic")
@Controller("shopee-logistic")
@Resource(Controllers.SHOPEE_LOGISTIC)
@Roles({
  roles: [Role.AdminEcom, `realm:app-${Role.AdminEcom}`, Role.Admin, `realm:app-${Role.Admin}`],
  mode: RoleMatchingMode.ANY,
})
export class ShopeeLogisticController {
  constructor(private readonly shopeeLogisticService: ShopeeLogisticService) {}

  @Get("sync-list/:storeId")
  @Scopes(Scope.GET)
  async syncList(@Param("storeId") storeId: string) {
    return await this.shopeeLogisticService.syncList(storeId);
  }

  @Get("")
  @Scopes(Scope.GET)
  async getList(@Query() query: LogisticListDto) {
    return await this.shopeeLogisticService.getList(query);
  }
}
