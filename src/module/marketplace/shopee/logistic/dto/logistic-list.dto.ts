import { ApiProperty } from "@nestjs/swagger";
import { IsBoolean, IsMongoId, IsNotEmpty, ValidateIf } from "class-validator";
import { PaginationParamDto } from "../../../../../common/pagination-param.dto";
import { TransformBoolean } from "../../../../../common/decorator/transform-boolean.decorator";

export class LogisticListDto extends PaginationParamDto {
  @ApiProperty({ required: false })
  @ValidateIf((obj) => obj.enabled)
  @IsBoolean()
  @TransformBoolean()
  enabled?: boolean;

  @ApiProperty({ required: false })
  @ValidateIf((obj) => obj.enabled)
  @IsBoolean()
  @TransformBoolean()
  cod?: number;

  @ApiProperty({ required: false })
  name?: string;

  @ApiProperty({})
  @IsNotEmpty()
  @IsMongoId()
  storeId: string;
}
