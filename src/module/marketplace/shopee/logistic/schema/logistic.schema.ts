import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import * as mongoose from "mongoose";
import { Document } from "mongoose";
import * as mongoosePaginate from "mongoose-paginate-v2";
import { MarketplaceStoreList } from "../../../schema/store-list.schema";

@Schema({ timestamps: true })
export class ShopeeLogistic {
  @Prop({ index: true })
  logisticId: number;

  @Prop()
  name: string;

  @Prop()
  cod: boolean;

  @Prop({ index: true })
  enabled: boolean;

  @Prop()
  description: string;

  @Prop()
  sizeList: Array<Record<string, any>>;

  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: MarketplaceStoreList.name,
  })
  storeId: string;
}

export type ShopeeLogisticDocument = ShopeeLogistic & Document;

export const ShopeeLogisticSchema = SchemaFactory.createForClass(ShopeeLogistic);
ShopeeLogisticSchema.plugin(mongoosePaginate);
