import { InjectModel } from "@nestjs/mongoose";
import { PaginateModel } from "mongoose";
import { isMongoId } from "class-validator";
import { HttpException, HttpStatus } from "@nestjs/common";
import { ShopeeStoreService } from "../store/store.service";
import { ShopeeLogistic, ShopeeLogisticDocument } from "./schema/logistic.schema";
import { ShopeeApiLogisticService } from "../../../microservices/shopee";
import { LogisticListDto } from "./dto/logistic-list.dto";

export class ShopeeLogisticService {
  constructor(
    @InjectModel(ShopeeLogistic.name)
    private readonly logisticModel: PaginateModel<ShopeeLogisticDocument>,
    private readonly shopeeLogisticService: ShopeeApiLogisticService,
    private readonly storeService: ShopeeStoreService,
  ) {}

  async syncList(storeId: string) {
    if (!isMongoId(storeId)) {
      throw new HttpException("store id must be a mongoID", HttpStatus.BAD_REQUEST);
    }

    const store = await this.storeService.storeDetail(storeId);
    const logistic = await this.shopeeLogisticService.getList(store.storeId);
    const mock = logistic.map((l) => {
      return {
        logisticId: l.logistics_channel_id,
        name: l.logistics_channel_name,
        cod: l.cod_enabled,
        enabled: l.enabled,
        description: l.logistics_description,
        storeId: store._id,
        sizeList: l.size_list,
      };
    });

    await this.logisticModel.insertMany(mock);

    return logistic;
  }

  async getList(payload: LogisticListDto) {
    const filter: Record<string, any> = { storeId: payload.storeId };

    if (payload.name) filter.name = new RegExp(payload.name, "i");
    if (typeof payload.enabled === "boolean") filter.enabled = payload.enabled;
    if (typeof payload.cod === "boolean") filter.cod = payload.cod;

    return await this.logisticModel.paginate(filter, {
      page: payload.page || 1,
      limit: payload.limit || 10,
      lean: true,
      forceCountFn: true,
    });
  }
}
