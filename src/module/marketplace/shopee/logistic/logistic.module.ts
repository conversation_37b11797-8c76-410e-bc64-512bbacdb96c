import { <PERSON>du<PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { ShopeeLogisticController } from "./logistic.controller";
import { ShopeeLogisticService } from "./logistic.service";
import { ShopeeLogistic, ShopeeLogisticSchema } from "./schema/logistic.schema";
import { ShopeeApiModule } from "../../../microservices/shopee/shopee.module";
import { ApiUtilsModule } from "../../../microservices/utils/api-utils.module";
import { ShopeeStoreModule } from "../store/store.module";

@Module({
  controllers: [ShopeeLogisticController],
  providers: [ShopeeLogisticService],
  imports: [
    MongooseModule.forFeature([
      {
        name: ShopeeLogistic.name,
        schema: ShopeeLogisticSchema,
      },
    ]),
    ShopeeApiModule,
    ApiUtilsModule,
    ShopeeStoreModule,
  ],
  exports: [ShopeeLogisticService],
})
export class ShopeeLogisticModule {}
