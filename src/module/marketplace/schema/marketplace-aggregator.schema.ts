import { Prop, raw, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Document, SchemaTypes } from "mongoose";
import * as mongoosePaginate from "mongoose-paginate-v2";

export interface Payment {
  method: string;
  totalAmount: number;
  discountSellerAmount: number;
  voucherSellerDiscount: number;
  discountPlatformAmount: number;
  voucherPlatformDiscount: number;
  paymentStatus: string;
  totalFee: number;
  shipmentFee: number;
  shipmentFeeDiscount: number;
  subtotal: string;
  feeBreakdown: Array<Record<string, any>> | Record<string, any>;
  discountBreakdown: Array<Record<string, any>> | Record<string, any>;
}

@Schema({ _id: false })
class ItemLines {
  @Prop()
  id: string;

  @Prop()
  name: string;

  @Prop()
  sku: string;

  @Prop()
  variantName: string;

  @Prop()
  variantId: string;

  @Prop()
  variantSku: string;

  @Prop()
  qty: number;

  @Prop()
  price: number;

  @Prop()
  totalPrice: number;

  @Prop()
  sellerDiscount: number;

  @Prop()
  sellerVoucherDiscount: number;

  @Prop()
  marketplaceDiscount: number;

  @Prop()
  coinRebate: number;

  @Prop()
  voucherCode: string;

  @Prop()
  weight: number;

  @Prop({ type: SchemaTypes.Mixed })
  promotion: Array<Record<string, any>> | Record<string, any>;

  @Prop({ type: SchemaTypes.Mixed })
  sellerDiscountBreakdown: Array<Record<string, any>> | Record<string, any>;

  @Prop()
  packageStatus: string;

  @Prop()
  tax: number;

  @Prop()
  isGift: string;

  @Prop({ type: SchemaTypes.Mixed })
  imageUrl: Array<Record<string, any>> | Record<string, any>;
}

const ItemLinesSchema = SchemaFactory.createForClass(ItemLines);

@Schema({
  timestamps: true,
})
export class MarketplaceAggregator {
  @Prop({ required: true, unique: true })
  maId: string;

  @Prop()
  orderStatus: string;

  @Prop()
  channel: string;

  @Prop()
  channelId: number;

  @Prop()
  marketplaceCode: string;

  @Prop()
  orderId: string;

  @Prop()
  companyId: string;

  @Prop({
    type: SchemaTypes.Mixed,
  })
  payments: Payment;

  @Prop()
  orderedAt: Date;

  @Prop()
  paidAt: Date;

  @Prop()
  createdAt: Date;

  @Prop()
  updatedAt: Date;

  @Prop()
  integrationDate: Date;

  @Prop([ItemLinesSchema])
  itemLines: Array<ItemLines>;

  @Prop(
    raw({
      awb: { type: String },
      documentPath: { type: String, required: false },
      bookingCode: { type: String, required: false },
      deliveryType: { type: String },
      channelDocsPath: { type: String, required: false },
      logisticDestinationCode: { type: String, required: false },
      shippingProvider: { type: String },
      shippingProviderType: { type: String },
      expedition: { type: String },
      city: { type: String },
      postalCode: { type: String },
      formattedAddress: { type: String },
    }),
  )
  shippingCourier: Record<string, any>;

  @Prop(
    raw({
      address_1: { type: String },
      city: { type: String },
      name: { type: String },
      phone: { type: String },
      postalCode: { type: String },
      province: { type: String },
      provinceCode: { type: String },
      subDistrict: { type: String },
      district: { type: String },
      formatted: { type: String },
    }),
  )
  address: Record<string, any>;

  @Prop(
    raw({
      id: { type: Number, required: false },
      name: { type: String },
      email: { type: String, required: false },
      customerSince: { type: Date, required: false },
    }),
  )
  customerInfo: Record<string, any>;

  @Prop()
  goldStatus?: string;

  @Prop()
  goldNoteNumber?: string;

  @Prop()
  orderNumber?: string;

  @Prop()
  warehouseId: number;

  @Prop()
  shippingPrice: number;

  @Prop()
  storeId: number;

  @Prop()
  shippingProvider: string;

  @Prop()
  shippingProviderType: string;
}

export type MarketplaceAggregatorDocument = MarketplaceAggregator & Document;

export const MarketplaceAggregatorSchema = SchemaFactory.createForClass(MarketplaceAggregator);

MarketplaceAggregatorSchema.plugin(mongoosePaginate);
