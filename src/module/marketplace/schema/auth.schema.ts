import { <PERSON>p, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Document } from "mongoose";
import { MarketplacePlatformEnum } from "../../../enum/marketplace.enum";

@Schema({ timestamps: true })
export class MarketplaceAuth {
  @Prop({ enum: MarketplacePlatformEnum })
  platform: MarketplacePlatformEnum;

  @Prop()
  storeId: string;

  @Prop()
  accessToken: string;

  @Prop()
  accessTokenExp: Date;

  @Prop()
  refreshToken: string;

  @Prop()
  refreshTokenExp: Date;
}

export type MarketplaceAuthDocument = MarketplaceAuth & Document;
export const MarketplaceAuthSchema = SchemaFactory.createForClass(MarketplaceAuth);

MarketplaceAuthSchema.index({ platform: 1, storeId: 1 }, { unique: true });
