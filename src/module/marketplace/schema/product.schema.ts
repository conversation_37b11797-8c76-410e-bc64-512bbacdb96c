import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import * as mongoose from "mongoose";
import { Document } from "mongoose";
import * as mongoosePaginate from "mongoose-paginate-v2";
import { MarketplacePlatformEnum } from "../../../enum/marketplace.enum";
import { MarketplaceStoreList } from "./store-list.schema";

@Schema({ _id: false, timestamps: false })
class Dimension {
  @Prop()
  width: number;

  @Prop()
  length: number;

  @Prop()
  height: number;
}
const DimensionSchema = SchemaFactory.createForClass(Dimension);

@Schema({ _id: false, timestamps: false })
class Wholesale {
  @Prop()
  minQty: number;

  @Prop()
  maxQty: number;

  @Prop()
  price: number;
}
const WholesaleSchema = SchemaFactory.createForClass(Wholesale);

@Schema({ _id: false, timestamps: false })
class PlatformIdObject {
  @Prop({ enum: MarketplacePlatformEnum })
  platform: MarketplacePlatformEnum;

  @Prop()
  id: number;

  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: MarketplaceStoreList.name,
  })
  storeId: string;
}
const PlatformIdObjectSchema = SchemaFactory.createForClass(PlatformIdObject);

@Schema({ _id: false, timestamps: false })
class Images {
  @Prop({ enum: MarketplacePlatformEnum })
  platform: MarketplacePlatformEnum;

  @Prop({ type: mongoose.SchemaTypes.Mixed })
  data: { ids: Array<string>; urls: Array<string> };

  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: MarketplaceStoreList.name,
  })
  storeId: string;
}
const ImagesSchema = SchemaFactory.createForClass(Images);

@Schema({ timestamps: true })
export class MarketplaceProduct {
  @Prop({ type: [PlatformIdObjectSchema] })
  marketplaceIds: Array<PlatformIdObject>;

  @Prop()
  name: string;

  @Prop()
  description: string;

  @Prop({ type: [ImagesSchema] })
  images: Array<Images>;

  @Prop()
  videos: Array<Record<string, any>>;

  @Prop({ type: [PlatformIdObjectSchema] })
  categories: Array<PlatformIdObject>;

  @Prop()
  brandName: string;

  @Prop()
  oriPrice: number;

  @Prop()
  currPrice: number;

  @Prop()
  stock: number;

  @Prop()
  weight: number;

  @Prop({ type: DimensionSchema })
  dimension: Dimension;

  @Prop({ unique: true })
  sku: string;

  @Prop({ type: [WholesaleSchema] })
  wholesale: Array<Wholesale>;

  @Prop()
  isPreOrder: boolean;
}

export type MarketplaceProductDocument = MarketplaceProduct & Document;

export const MarketplaceProductSchema = SchemaFactory.createForClass(MarketplaceProduct);
MarketplaceProductSchema.plugin(mongoosePaginate);
