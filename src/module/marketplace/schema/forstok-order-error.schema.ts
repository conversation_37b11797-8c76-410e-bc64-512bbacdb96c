import { Prop, raw, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Document } from "mongoose";
import * as mongoosePaginate from "mongoose-paginate-v2";

@Schema({ _id: false })
class ItemLines {
  @Prop()
  id: number;

  @Prop()
  localId: string;

  @Prop()
  sku: string;

  @Prop()
  name: string;

  @Prop()
  variantName: string;

  @Prop()
  variantId: number;

  @Prop()
  variantSku: string;

  @Prop()
  price: number;

  @Prop()
  salePrice: number;

  @Prop()
  totalPrice: number;

  @Prop()
  voucherAmount: number;

  @Prop()
  voucherCode: string;

  @Prop()
  voucherSeller: number;

  @Prop()
  taxPrice: number;

  @Prop()
  fulfillByChannel: boolean;

  @Prop()
  shippingProvider: string;

  @Prop()
  shippingProviderType: string;

  @Prop()
  trackingNumber: string;

  @Prop()
  note: string;

  @Prop()
  internalNote: string;

  @Prop()
  qty: number;
}

const ItemLinesSchema = SchemaFactory.createForClass(ItemLines);

@Schema({
  timestamps: true,
})
export class ForstokOrderError extends Document {
  @Prop({ required: true, unique: true })
  forstokOrderId: number;

  @Prop()
  status: string;

  @Prop()
  channel: string;

  @Prop()
  channelId: number;

  @Prop()
  localId: string;

  @Prop()
  localName: string;

  @Prop()
  storeName: string;

  @Prop()
  storeId: number;

  @Prop()
  profileId: number;

  @Prop(
    raw({
      address_1: { type: String },
      address_2: { type: String },
      city: { type: String },
      country: { type: String },
      name: { type: String },
      phone: { type: String },
      postalCode: { type: String },
      province: { type: String },
      provinceCode: { type: String },
      subDistrict: { type: String },
      district: { type: String },
      formatted: { type: String },
    }),
  )
  address: Record<string, any>;

  @Prop(
    raw({
      id: { type: Number },
      name: { type: String },
      email: { type: String },
      customerSince: { type: Date },
    }),
  )
  customerInfo: Record<string, any>;

  @Prop()
  orderedAt: Date;

  @Prop()
  createdAt: Date;

  @Prop()
  updatedAt: Date;

  @Prop([ItemLinesSchema])
  itemLines: Array<ItemLines>;

  @Prop(
    raw({
      paymentMethod: { type: String },
      status: { type: String },
    }),
  )
  payment: Record<string, any>;

  @Prop()
  shippingPrice: number;

  @Prop()
  discShippingSeller: number;

  @Prop()
  discShippingPlatform: number;

  @Prop(
    raw({
      awb: { type: String },
      documentPath: { type: String },
      bookingCode: { type: String },
      deliveryType: { type: String },
      channelDocsPath: { type: String },
      logisticDestinationCode: { type: String },
      shippingProvider: { type: String },
      shippingProviderType: { type: String },
      expedition: { type: String },
      city: { type: String },
      postalCode: { type: String },
      formattedAddress: { type: String },
    }),
  )
  shippingCourier: Record<string, any>;

  @Prop()
  shippingProvider: string;

  @Prop()
  shippingProviderType: string;

  @Prop()
  shippingDescription: string;

  @Prop()
  subtotal: number;

  @Prop()
  channelRebate: number;

  @Prop()
  cashless: boolean;

  @Prop()
  discountAmount: number;

  @Prop()
  totalPrice: number;

  @Prop()
  voucherCode: string;

  @Prop()
  voucherSeller: number;

  @Prop()
  insuranceFee: number;

  @Prop()
  discountReason: string;

  @Prop()
  taxPrice: number;

  @Prop()
  warehouseId: number;

  @Prop()
  cod: boolean;

  @Prop()
  deliveryType: string;

  @Prop()
  warehouseCode: string;

  @Prop()
  note: string;

  @Prop()
  internalNote: string;

  @Prop()
  goldStatus: string;

  @Prop()
  goldNoteNumber: string;

  @Prop()
  orderNumber: string;

  @Prop({ default: new Date() })
  integrationDate: Date;

  @Prop()
  errorMessage: string;
}

export type ForstokOrderErrorDocument = ForstokOrderError & Document;

export const ForstokOrderErrorSchema = SchemaFactory.createForClass(ForstokOrderError);

ForstokOrderErrorSchema.plugin(mongoosePaginate);
