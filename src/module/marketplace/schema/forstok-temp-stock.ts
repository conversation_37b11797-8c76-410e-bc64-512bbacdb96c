import { <PERSON><PERSON>, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Document } from "mongoose";

@Schema()
export class ForstokTempStock {
  @Prop({ required: true })
  sku: string;

  @Prop({ required: true })
  warehouse_id: string;

  @Prop({ required: true })
  qty: number;
}

export type ForstokTempStockDocument = ForstokTempStock & Document;

export const ForstokTempStockSchema = SchemaFactory.createForClass(ForstokTempStock);
