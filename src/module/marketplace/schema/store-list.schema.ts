import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Document } from "mongoose";
import { MarketplacePlatformEnum } from "../../../enum/marketplace.enum";
import * as mongoosePaginate from "mongoose-paginate-v2";

@Schema({ timestamps: true })
export class MarketplaceStoreList {
  @Prop({ index: true, enum: MarketplacePlatformEnum })
  platform: MarketplacePlatformEnum;

  @Prop()
  storeId: string;

  @Prop()
  storeName: string;

  @Prop()
  storeLogo: string;

  @Prop()
  description: string;

  @Prop({ default: null })
  deletedAt?: Date;
}

export type MarketplaceStoreListDocument = MarketplaceStoreList & Document;
export const MarketplaceStoreListSchema = SchemaFactory.createForClass(MarketplaceStoreList);

MarketplaceStoreListSchema.index({ platform: 1, storeId: 1 }, { unique: true });
MarketplaceStoreListSchema.plugin(mongoosePaginate);
