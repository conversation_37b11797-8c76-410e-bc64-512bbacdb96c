import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import mongoose, { Document } from "mongoose";
import { MarketplaceStoreList } from "./store-list.schema";
import { MarketplaceProduct } from "./product.schema";

@Schema({ _id: false })
class ImageRecord {
  @Prop()
  ids: Array<string>;

  @Prop()
  urls: Array<string>;
}
const ImageRecordSchema = SchemaFactory.createForClass(ImageRecord);

@Schema()
class Record {
  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: MarketplaceStoreList.name,
  })
  storeId: string;

  @Prop({ type: ImageRecordSchema })
  oldImages: Array<ImageRecord>;

  @Prop({ type: ImageRecordSchema })
  newImages: Array<ImageRecord>;

  @Prop()
  lastUpdate: Date;
}
const RecordSchema = SchemaFactory.createForClass(Record);

@Schema({ timestamps: true })
export class ProductImageUpdate {
  @Prop({
    unique: true,
    type: mongoose.Schema.Types.ObjectId,
    ref: MarketplaceProduct.name,
  })
  productId: string;

  @Prop({ index: true })
  sku: string;

  @Prop({ type: [RecordSchema] })
  record: Array<Record>;
}

export type ProductImageUpdateDocument = ProductImageUpdate & Document;
export const ProductImageUpdateSchema = SchemaFactory.createForClass(ProductImageUpdate);
