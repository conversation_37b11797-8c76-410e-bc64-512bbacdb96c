import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>hemaFactory } from "@nestjs/mongoose";
import { Document } from "mongoose";

@Schema({ timestamps: false })
export class Config {
  @Prop({ required: true })
  group: string;

  @Prop({ required: true })
  key: string;

  @Prop({ required: true })
  value: string;
}

export type ConfigDocument = Config & Document;

export const ConfigSchema = SchemaFactory.createForClass(Config);
ConfigSchema.index({ group: 1, key: 1 }, { unique: true });
