import { <PERSON>p, Schem<PERSON>, SchemaFactory } from "@nestjs/mongoose";
import { Document } from "mongoose";
import { MarketplacePlatformEnum } from "../../../enum/marketplace.enum";
import * as mongoosePaginate from "mongoose-paginate-v2";

@Schema({ timestamps: true })
export class PushOrderLog {
  @Prop()
  name: string;

  @Prop({index: true})
  keycloakUserId: string;

  @Prop()
  email: string;

  @Prop()
  forstokOrderId: string;

  @Prop()
  itemSubtitute: Array<string>;
}

export type PushOrderLogDocument = PushOrderLog & Document;
export const PushOrderLogSchema = SchemaFactory.createForClass(PushOrderLog);

PushOrderLogSchema.index({ storeName: 1 });
PushOrderLogSchema.plugin(mongoosePaginate);
