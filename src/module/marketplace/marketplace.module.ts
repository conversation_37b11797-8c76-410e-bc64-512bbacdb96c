import { Module } from "@nestjs/common";
import { MarketplaceService } from "./marketplace.service";
import { MarketplaceController } from "./marketplace.controller";
import { ConfigModule } from "@nestjs/config";
import { AwsS3Module } from "../microservices/aws-s3/aws-s3.module";
import { ShopeeProductModule } from "./shopee/product/product.module";
import { ShopeeStoreModule } from "./shopee/store/store.module";
import { ShopeeAuthModule } from "./shopee/auth/auth.module";
import { MongooseModule } from "@nestjs/mongoose";
import { ProductImageUpdate, ProductImageUpdateSchema } from "./schema/product-image-update.schema";
import { ForstokOrderError, ForstokOrderErrorSchema } from "./schema/forstok-order-error.schema";
import { Callback, CallbackSchema } from "../callback/schema/callback.schema";
import { DEFAULT_REDIS_NAMESPACE, RedisModule } from "@liaoliaots/nestjs-redis";
import { RedisNsAutoRts } from "src/enum/callback.enum";
import { CallbackModule } from "../callback/callback.module";
import { ApiOmsModule } from "../microservices/oms/api-oms.module";
import { ForstokOrder, ForstokOrderSchema } from "./schema/forstok-order.schema";
import { PushOrderLog, PushOrderLogSchema } from "./schema/push-order-log.schema";
import { ApiUsersModule } from "../microservices/users/api-users.module";

@Module({
  controllers: [MarketplaceController],
  providers: [MarketplaceService],
  imports: [
    MongooseModule.forFeature([
      {
        name: ProductImageUpdate.name,
        schema: ProductImageUpdateSchema,
      },
      {
        name: ForstokOrderError.name,
        schema: ForstokOrderErrorSchema,
      },
      {
        name: Callback.name,
        schema: CallbackSchema,
      },
      {
        name: ForstokOrder.name,
        schema: ForstokOrderSchema,
      },
      {
        name: PushOrderLog.name,
        schema: PushOrderLogSchema,
      },
    ]),
    ShopeeProductModule,
    CallbackModule,
    ApiUsersModule,
    ApiOmsModule,
    ShopeeStoreModule,
    AwsS3Module,
    ConfigModule,
    ShopeeAuthModule,
    RedisModule.forRoot({
      config: [
        {
          port: +process.env.REDIS_MP_PORT,
          host: process.env.REDIS_MP_HOST,
          db: +process.env.REDIS_STOCK_DB,
          namespace: DEFAULT_REDIS_NAMESPACE,
        },
        {
          port: +process.env.REDIS_RTS_FORSTOK_PORT,
          host: process.env.REDIS_RTS_FORSTOK_HOST,
          db: +process.env.REDIS_RTS_FORSTOK_DB,
          namespace: RedisNsAutoRts,
        },
      ],
    }),
  ],
})
export class MarketplaceModule {}
