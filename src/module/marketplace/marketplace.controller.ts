import { ApiBearerAuth, ApiTags } from "@nestjs/swagger";
import { Body, Controller, Get, Param, Patch, Post, Query, Req } from "@nestjs/common";
import { Controllers } from "../../enum/rbac.enum";
import { InternalAccess, Resource } from "keycloak-connect-tbs";
import { MarketplaceService } from "./marketplace.service";
import { SyncProductPromoImgDto } from "./dto/sync-product-promo-img.dto";
import { FilterForstokOrderErrorDto } from "./dto/filter-forstock-order-error.dto";
import { UpdateForstokOrder } from "./dto/update-forstok-order.dto";

@ApiTags("Marketplaces")
@Controller("marketplace")
@Resource(Controllers.MARKETPLACE)
export class MarketplaceController {
  constructor(private readonly marketplaceService: MarketplaceService) {}

  @Post("sync-product-promo-image")
  async syncProductPromoImage(@Body() body: SyncProductPromoImgDto) {
    return await this.marketplaceService.updateProductPromoImage(body);
  }

  @Get("forstok-order-error")
  @ApiBearerAuth("access-token")
  async getForstokOrderError(@Query() query: FilterForstokOrderErrorDto) {
    return await this.marketplaceService.getForstokOrderError(query);
  }

  @Get("forstok-order-error/:id")
  @ApiBearerAuth("access-token")
  async getForstokOrderErrorDetail(@Param("id") id: string) {
    return await this.marketplaceService.getForstockDetail(id);
  }

  @Patch("push-forstok-order/:forstokOrderId")
  @ApiBearerAuth("access-token")
  async pushForstokOrder(
    @Param("forstokOrderId") forstokOrderId: number,
    @Body() payload: UpdateForstokOrder,
    @Req() req
  ) {
    return await this.marketplaceService.pushForstokOrder(forstokOrderId, payload, req);
  }
}
