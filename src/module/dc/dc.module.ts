import { Modu<PERSON> } from "@nestjs/common";
import { DcService } from "./dc.service";
import { DcController } from "./dc.controller";
import { ForstokModule } from "../forstok/forstok.module";
import { MongooseModule } from "@nestjs/mongoose";
import { ForstokOrder, ForstokOrderSchema } from "../marketplace/schema/forstok-order.schema";
import { ApiOmsModule } from "../microservices/oms/api-oms.module";
import { ApiUtilsModule } from "../microservices/utils/api-utils.module";

@Module({
  controllers: [DcController],
  providers: [DcService],
  imports: [
    MongooseModule.forFeature([
      {
        name: ForstokOrder.name,
        schema: ForstokOrderSchema,
      },
    ]),
    ApiOmsModule,
    ForstokModule,
    ApiUtilsModule,
  ],
})
export class DcModule {}
