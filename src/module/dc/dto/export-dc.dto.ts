import { ApiProperty } from "@nestjs/swagger";
import { ArrayMinSize, IsArray, IsEnum, IsNotEmpty, ValidateIf } from "class-validator";
import { Transform } from "class-transformer";
import { DCOrderStatus, ForstokChannel } from "../../../enum/forstok-salesorder-status.enum";
import { TransformArray } from "../../../common/decorator/transform-array.decorator";
import { TimezoneOffset } from "../../../common/decorator/timezone-offset-validator.dto";

export class ExportDcDto {
  @ApiProperty({ required: false })
  id: string;

  @ApiProperty({ required: false })
  @ValidateIf((obj) => obj.ids)
  @IsArray()
  @ArrayMinSize(1)
  @TransformArray()
  ids: Array<number>;

  @ApiProperty({ required: false, enum: DCOrderStatus })
  @ValidateIf((obj) => obj.status)
  @IsEnum(DCOrderStatus)
  status: string | Record<string, any>;

  @ApiProperty({ required: false })
  orderNumber: string;

  @ApiProperty({ required: false, enum: ForstokChannel })
  @ValidateIf((obj) => obj.channel)
  @IsEnum(ForstokChannel)
  @Transform(({ value }) => ForstokChannel[value])
  channel: string;

  @ApiProperty({ required: false })
  @IsNotEmpty()
  @TimezoneOffset()
  order_tz: string;
}
