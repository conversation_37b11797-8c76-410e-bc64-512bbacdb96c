import { ApiProperty } from "@nestjs/swagger";
import { IsEnum, IsNotEmpty, IsOptional, ValidateIf } from "class-validator";
import { DCOrderStatus, ForstokChannel } from "../../../enum/forstok-salesorder-status.enum";
import { PaginationParamDto } from "../../../common/pagination-param.dto";
import { Transform } from "class-transformer";
import { ValidDate } from "../../../common/decorator/date-validator.dto";
import { TimezoneOffset } from "../../../common/decorator/timezone-offset-validator.dto";

export class FindAllDcDto extends PaginationParamDto {
  @ApiProperty({ required: false, enum: DCOrderStatus })
  @ValidateIf((obj) => obj.status)
  @IsEnum(DCOrderStatus)
  status: string;

  @ApiProperty({ required: false })
  @IsOptional()
  orderNumber: string;

  @ApiProperty({ required: false, enum: ForstokChannel })
  @ValidateIf((obj) => obj.channel)
  @IsEnum(ForstokChannel)
  @Transform(({ value }) => ForstokChannel[value])
  channel: string;

  @ApiProperty({ required: false })
  @IsOptional()
  keyword: string;

  @ApiProperty({ required: false })
  @ValidateIf((obj) => obj.order_gte)
  @ValidDate()
  order_gte: string;

  @ApiProperty({ required: false })
  @ValidateIf((obj) => obj.order_lte)
  @ValidDate()
  order_lte: string;

  @ApiProperty({ required: false })
  @ValidateIf((obj) => obj.order_lte || obj.order_gte)
  @IsNotEmpty()
  @TimezoneOffset()
  order_tz: string;
}
