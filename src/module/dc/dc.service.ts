import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { ForstokOrder, ForstokOrderDocument } from "../marketplace/schema/forstok-order.schema";
import { PaginateModel } from "mongoose";
import { FindAllDcDto } from "./dto/findall-dc.dto";
import { emptyCheck, wait } from "../../utils/function.util";
import { ExportDcDto } from "./dto/export-dc.dto";
import writeXlsxFile from "write-excel-file/node";
import readXlsxFile, { Row } from "read-excel-file/node";
import { ApiOmsService } from "../microservices/oms/api-oms.service";
import {
  DCOrderStatus,
  ForstokChannel,
  ForstokStatus,
  OMSOrderStatus,
} from "../../enum/forstok-salesorder-status.enum";
import { ForstokService } from "../forstok/forstok.service";
import { GetMultipleDto } from "./dto/get-multiple.dto";
import * as dayjs from "dayjs";
import * as utc from "dayjs/plugin/utc";
import * as timezone from "dayjs/plugin/timezone";
import { UpdateDcDto } from "./dto/update-dc.dto";
import { ApiUtilsService } from "../microservices/utils/api-utils.service";

@Injectable()
export class DcService {
  constructor(
    @InjectModel(ForstokOrder.name) private readonly forstokModel: PaginateModel<ForstokOrderDocument>,
    private readonly omsService: ApiOmsService,
    private readonly forstokService: ForstokService,
    private readonly utilService: ApiUtilsService,
  ) {}

  async findAll(params: FindAllDcDto) {
    let filter: Record<string, any> = {
      status: {
        $in: [
          DCOrderStatus.ReadyToPick,
          DCOrderStatus.OrderPrepared,
          DCOrderStatus.ReadyToShip,
          DCOrderStatus.Shipped,
          DCOrderStatus.Delivered,
          DCOrderStatus.Complete,
          DCOrderStatus.Cancelled,
          DCOrderStatus.Completed,
        ],
      },
    };

    if (params.status) filter.status = params.status;
    if (params.status === DCOrderStatus.Complete) {
      filter.status = { $in: [DCOrderStatus.Complete, DCOrderStatus.Completed] };
    }
    if (params.orderNumber) filter.orderNumber = params.orderNumber;
    if (params.channel) filter.channelId = params.channel;
    if (params.keyword) {
      filter = {
        ...filter,
        $or: [
          { orderNumber: params.keyword },
          { "customerInfo.name": params.keyword },
          { localId: params.keyword },
          { localName: new RegExp(params.keyword, "i") },
        ],
      };
    }
    if (params.order_lte) {
      filter.orderedAt = { $lte: new Date(params.order_lte + "23:59:59") };
    }
    if (params.order_gte) {
      filter.orderedAt = { ...filter.orderedAt, $gte: params.order_gte + "00:00:00" };
    }

    return await this.forstokModel.paginate(filter, {
      page: params.page || 1,
      limit: params.limit || 1000,
      sort: params.sort,
      forceCountFn: true,
    });
  }

  async multiple(params: GetMultipleDto) {
    return await this.forstokModel.find({ _id: { $in: params.ids } }).exec();
  }

  async findOne(id: string) {
    return await this.forstokModel.findById(id).exec();
  }

  async update(id: string, body: UpdateDcDto) {
    const check = await this.forstokModel.findById(id);

    if (!check) {
      throw new HttpException("id tidak ditemukan", HttpStatus.BAD_REQUEST);
    }

    const mock: Record<string, any> = {};
    Object.keys(body).map((key) => {
      if (!emptyCheck(body[key])) mock[key] = body[key];
    });

    return await this.forstokModel.findByIdAndUpdate(id, mock, { new: true }).exec();
  }

  remove(id: string) {
    return `This action removes a #${id} dc`;
  }

  async print(params: ExportDcDto) {
    const header = [
      { value: "Order Number", fontWeight: "bold", align: "center" },
      { value: "Resi", fontWeight: "bold", align: "center" },
      { value: "Kurir", fontWeight: "bold", align: "center" },
      { value: "Kurir Service", fontWeight: "bold", align: "center" },
    ];

    const data = await this._fetchPrintData(params);

    const row: Array<Array<Record<string, any>>> = [header];
    data.map((item) => {
      row.push([{ value: item.orderNumber || "" }, { value: "" }, { value: "" }, { value: "" }]);
    });

    return await writeXlsxFile(row, {
      columns: [{ width: 20 }, { width: 15 }, { width: 100 }, { width: 50 }],
      buffer: true,
    });
  }

  async exportData(params: ExportDcDto) {
    dayjs.extend(utc);
    dayjs.extend(timezone);

    const header = [
      { value: "Order Number", fontWeight: "bold", align: "center" },
      { value: "Ship Name", fontWeight: "bold", align: "center" },
      { value: "Store Name", fontWeight: "bold", align: "center" },
      { value: "Purchase Date", fontWeight: "bold", align: "center" },
      { value: "Shiping Info", fontWeight: "bold", align: "center" },
      { value: "Shipping Price", fontWeight: "bold", align: "center" },
      { value: "Status", fontWeight: "bold", align: "center" },
      { value: "ID Marketplace", fontWeight: "bold", align: "center" },
      { value: "Marketplace Name", fontWeight: "bold", align: "center" },
      { value: "Integration Date", fontWeight: "bold", align: "center" },
      { value: "No Resi", fontWeight: "bold", align: "center" },
    ];

    if (!params.status) {
      params.status = {};
      params.status["$in"] = [
        DCOrderStatus.ReadyToPick,
        DCOrderStatus.OrderPrepared,
        DCOrderStatus.ReadyToShip,
        DCOrderStatus.Shipped,
        DCOrderStatus.Delivered,
        DCOrderStatus.Complete,
        DCOrderStatus.Completed,
      ];
    }

    const data = await this._fetchPrintData(params);

    const row: Array<Array<Record<string, any>>> = [header];
    const width = header.map((item) => item.value.length);
    for (let i = 0; i < data.length; i++) {
      const item = data[i];
      const mock = [
        { value: item.orderNumber || "" },
        { value: item.customerInfo?.name || "" },
        { value: item.storeName || "" },
        {
          value: item.orderedAt
            ? dayjs(item.orderedAt).utcOffset(params.order_tz.replace(":", "")).format("YYYY-MM-DD HH:mm:ss")
            : "",
        },
        { value: item.shippingCourier?.expedition || "" },
        { value: item.shippingPrice || "" },
        { value: item.status || "" },
        { value: item.channelId === ForstokChannel.Tokopedia ? item.localName || "" : item.localId || "" },
        {
          value: item.integrationDate
            ? dayjs(item.integrationDate).utcOffset(params.order_tz.replace(":", "")).format("YYYY-MM-DD HH:mm:ss")
            : "",
        },
        { value: item.shippingCourier?.awb || "" },
      ];
      row.push(mock);
      width.map((w, i) => {
        const length = mock[i].value.length + 3;
        if (length > w) width[i] = length;
      });
    }

    return await writeXlsxFile(row, {
      columns: width.map((item) => {
        return { width: item };
      }),
      buffer: true,
    });
  }

  async import(file: Buffer) {
    try {
      const header = {
        0: "Order Number",
        1: "Resi",
        2: "Kurir",
        3: "Kurir Service",
      };
      const rows = await readXlsxFile(Buffer.from(file.buffer));

      rows.shift();

      const orderNumbers = [];
      const awbs = [];
      await this._checkRowsField(rows, orderNumbers, awbs, header);
      await this._checkAwbFromExcel(rows);

      const [checksOrder, checkAwb] = await Promise.all([
        this.forstokModel.find({ orderNumber: { $in: orderNumbers } }).lean(),
        this.forstokModel.find({ "shippingCourier.awb": { $in: awbs } }).lean(),
      ]);

      const hashCheckOrder = Object.fromEntries(
        checksOrder.map((check) => [
          check.orderNumber,
          { status: check.status, shippingCourier: check.shippingCourier },
        ]),
      );

      const hashCheckAwb = Object.fromEntries(checkAwb.map((check) => [check.shippingCourier.awb, true]));
      await this._checkAwbFromOrder(rows, hashCheckAwb);

      const forstokToken = await this.forstokService.getAuthToken();
      let delayTime = await this.utilService.getConfig("mp.forstok_delay_rts", true);
      delayTime = Number(delayTime || "1000");

      let success = 0;
      let fail = 0;
      let skip = 0;
      for (let i = 0; i < rows.length; i++) {
        const row = rows[i];

        const [orderNumber, awb, shippingProvider, shippingProviderType] = row;
        const check = hashCheckOrder[orderNumber as string];

        if (check?.status === DCOrderStatus.OrderPrepared) {
          const update = await this.forstokModel.findOneAndUpdate(
            { orderNumber },
            { "shippingCourier.awb": awb, status: ForstokStatus.Shipped, shippingProvider, shippingProviderType },
            { new: true },
          );

          if (!update) {
            fail++;
          } else {
            const forstok_shipping_courier = check.shippingCourier;
            forstok_shipping_courier.awb = awb;
            forstok_shipping_courier.shippingProvider = shippingProvider;
            forstok_shipping_courier.shippingProviderType = shippingProviderType;

            await this.omsService.updateOrder({
              orderNumber: orderNumber as string,
              status: OMSOrderStatus.OnShipment,
              updatedAt: new Date().toISOString(),
              forstok_shipping_courier,
            });

            if (process.env.NODE_ENV.toUpperCase() === "PRODUCTION") {
              await this.forstokService.setReadyToShip({
                token: forstokToken,
                orderId: update.forstokOrderId,
                courier: shippingProvider as string,
                awb: awb as string,
              });
            }

            success++;
          }
        } else if (!check) {
          fail++;
        } else {
          skip++;
        }

        await wait(delayTime);
      }

      return { success, fail, skip };
    } catch (err) {
      throw err;
    }
  }

  async pick(id) {
    const data = await this.forstokModel.findByIdAndUpdate(id, { status: DCOrderStatus.OrderPrepared }, { new: true });

    await this.omsService.updateOrder({
      orderNumber: data.orderNumber,
      status: OMSOrderStatus.OnProgress,
      updatedAt: new Date().toISOString(),
    });

    return data;
  }

  async bulkPick(ids: Array<string>) {
    const session = await this.forstokModel.startSession();
    try {
      session.startTransaction();

      const mockOms = [];
      const update = await Promise.all(
        ids.map(async (id) => {
          const data = await this.forstokModel
            .findByIdAndUpdate(id, { status: DCOrderStatus.OrderPrepared }, { new: true })
            .exec();

          mockOms.push({ orderNumber: data.orderNumber, status: OMSOrderStatus.OnProgress });
        }),
      );

      await this.omsService.bulkUpdateOrder(mockOms);

      await session.commitTransaction();
      return update;
    } catch (err) {
      await session.abortTransaction();
      throw err;
    } finally {
      await session.endSession();
    }
  }

  async pickAll() {
    const session = await this.forstokModel.startSession();
    try {
      session.startTransaction();
      const data = await this.forstokModel.find({ status: DCOrderStatus.ReadyToPick }, "_id, orderNumber").lean();

      const mockOms = [];
      const ids = [];
      data.map((item) => {
        mockOms.push({ orderNumber: item.orderNumber, status: OMSOrderStatus.OnProgress });
        ids.push(item._id);
      });

      const update = await this.forstokModel.updateMany(
        { _id: { $in: ids } },
        { status: DCOrderStatus.OrderPrepared },
        { session },
      );

      await this.omsService.bulkUpdateOrder(mockOms);

      await session.commitTransaction();
      return update;
    } catch (err) {
      await session.abortTransaction();
      throw err;
    } finally {
      await session.endSession();
    }
  }

  async _fetchPrintData(params: ExportDcDto) {
    const filter: Record<string, any> = { orderNumber: { $nin: [null, ""] } };

    if (params.id) filter._id = params.id;
    if (params.ids) filter._id = { $in: params.ids };
    if (params.orderNumber) filter.orderNumber = params.orderNumber;
    if (params.channel) filter.channelId = params.channel;
    if (params.status) filter.status = params.status;
    if (params.status === DCOrderStatus.Complete) {
      filter.status = { $in: [DCOrderStatus.Complete, DCOrderStatus.Completed] };
    }

    return await this.forstokModel.find(filter).lean().exec();
  }

  private async _checkRowsField(
    rows: Row[],
    orderNumbers: Array<string>,
    awbs: Array<string>,
    header: Record<string, any>,
  ) {
    const errorRow = [];

    await Promise.all(
      rows.map((row, i) => {
        const errorCol = [];
        for (let j = 0; j < row.length; j++) {
          if (!row[j]) errorCol.push(header[j]);
        }

        if (errorCol.length) {
          errorRow.push("Baris ke " + (i + 2) + " error dengan detail " + errorCol.join(", ") + " harus terisi");
        }

        orderNumbers.push(row[0] as string);
        awbs.push(row[1] as string);
      }),
    );

    if (errorRow.length) {
      throw new HttpException(errorRow.join("\n"), HttpStatus.BAD_REQUEST);
    }
  }

  private async _checkAwbFromOrder(rows: Row[], checkAwb: Record<string, any>) {
    const errorRow = [];

    await Promise.all(
      rows.map((row, i) => {
        if (checkAwb[row[1] as string]) {
          errorRow.push("Baris ke " + (i + 2) + " error dengan detail awb sudah terdaftar");
        }
      }),
    );

    if (errorRow.length) {
      throw new HttpException(errorRow.join("\n"), HttpStatus.BAD_REQUEST);
    }
  }

  private async _checkAwbFromExcel(rows: Row[]) {
    const check: Record<string, any> = {};
    const errorRow = [];

    await Promise.all(
      rows.map((row, i) => {
        const awb = row[1] as string;
        if (check[awb]) {
          errorRow.push("Baris ke " + (i + 2) + " error dengan detail awb sudah digunakan di baris " + check[awb]);
        } else {
          check[awb] = i + 2;
        }
      }),
    );

    if (errorRow.length) {
      throw new HttpException(errorRow.join("\n"), HttpStatus.BAD_REQUEST);
    }
  }
}
