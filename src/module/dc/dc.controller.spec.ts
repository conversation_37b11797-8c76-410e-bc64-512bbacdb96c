import { Test, TestingModule } from '@nestjs/testing';
import { <PERSON><PERSON><PERSON><PERSON>roller } from './dc.controller';
import { DcService } from './dc.service';

describe('DcController', () => {
  let controller: DcController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [DcController],
      providers: [DcService],
    }).compile();

    controller = module.get<DcController>(DcController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
