import {
  Controller,
  Get,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  HttpException,
  HttpStatus,
  Post,
  UploadedFile,
  UseInterceptors,
} from "@nestjs/common";
import { DcService } from "./dc.service";
import { UpdateDcDto } from "./dto/update-dc.dto";
import { FindAllDcDto } from "./dto/findall-dc.dto";
import { ApiBearerAuth, ApiConsumes, ApiTags } from "@nestjs/swagger";
import { ExportDcDto } from "./dto/export-dc.dto";
import { ImportDto } from "./dto/import.dto";
import { FileInterceptor } from "@nestjs/platform-express";
import { BulkPickDto } from "./dto/bulk-pick.dto";
import { GetMultipleDto } from "./dto/get-multiple.dto";
import { Resource, Scopes } from "keycloak-connect-tbs";
import { <PERSON><PERSON>, Scope } from "../../enum/rbac.enum";

@ApiTags("DC")
@ApiBearerAuth("access-token")
@Controller("dc")
@Resource(Controllers.DC)
export class DcController {
  constructor(private readonly dcService: DcService) {}

  @Get()
  @Scopes(Scope.GET)
  findAll(@Query() query: FindAllDcDto) {
    return this.dcService.findAll(query);
  }

  @Get("multiple")
  @Scopes(Scope.GET)
  multiple(@Query() query: GetMultipleDto) {
    return this.dcService.multiple(query);
  }

  @Get("download-template")
  @Scopes(Scope.GET)
  printExcel(@Query() query: ExportDcDto) {
    let check = false;
    const key = Object.keys(query);
    for (let i = 0; i < key.length; i++) {
      if (query[key[i]]) {
        check = true;
        break;
      }
    }

    if (!check) {
      throw new HttpException("filter harus terisi minimal 1", HttpStatus.BAD_REQUEST);
    }

    return this.dcService.print(query);
  }

  @Get("export")
  @Scopes(Scope.GET)
  exportData(@Query() query: ExportDcDto) {
    return this.dcService.exportData(query);
  }

  @Get(":id")
  @Scopes(Scope.GET)
  findOne(@Param("id") id: string) {
    return this.dcService.findOne(id);
  }

  @Post("import")
  @ApiConsumes("multipart/form-data")
  @UseInterceptors(FileInterceptor("file"))
  @Scopes(Scope.POST)
  import(@UploadedFile("file") file: any, @Body() body: ImportDto) {
    body.file = file;
    return this.dcService.import(file);
  }

  @Post("bulk-pick")
  @Scopes(Scope.POST)
  bulkPick(@Query() query: BulkPickDto) {
    return this.dcService.bulkPick(query.ids);
  }

  @Post("pick-all")
  @Scopes(Scope.POST)
  pickAll() {
    return this.dcService.pickAll();
  }

  @Patch("pick/:id")
  @Scopes(Scope.PATCH)
  pick(@Param("id") id: string) {
    return this.dcService.pick(id);
  }

  @Patch(":id")
  @Scopes(Scope.PATCH)
  update(@Param("id") id: string, @Body() updateDcDto: UpdateDcDto) {
    return this.dcService.update(id, updateDcDto);
  }

  @Delete(":id")
  @Scopes(Scope.DELETE)
  remove(@Param("id") id: string) {
    return this.dcService.remove(id);
  }
}
