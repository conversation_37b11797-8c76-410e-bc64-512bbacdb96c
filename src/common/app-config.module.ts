/*
https://docs.nestjs.com/modules
*/

import { Module } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import * as Jo<PERSON> from "joi";
import { AppConfigService } from "./app-config.service";

@Module({
  exports: [AppConfigService],
  imports: [
    ConfigModule.forRoot({
      envFilePath: `${process.env.NODE_ENV ? process.env.NODE_ENV : ""}.env`,
      validationSchema: Joi.object({
        APPS_PORT: Joi.number().required().default(3000),
        MONGO_URL: Joi.string().required(),
        FORSTOK_INTEGRATION_URL: Joi.string().required(),
        FORSTOK_ACCOUNTS_URL: Joi.string().required(),
        FORSTOK_ACCOUNT: Joi.string().required(),
        FORSTOK_PASSWORD: Joi.string().required(),
        FORSTOK_SECRET: Joi.string().required(),
      }),
    }),
  ],
  providers: [AppConfigService],
})
export class AppConfigModule {}
