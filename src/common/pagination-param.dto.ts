import { ApiProperty } from "@nestjs/swagger";
import { IsOptional, IsPositive, IsString, Max, Min } from "class-validator";
import { Transform, Type } from "class-transformer";

export class PaginationParamDto {
  @ApiProperty({ required: false, default: 1 })
  @IsOptional()
  @IsPositive()
  @Min(1, { message: "Minimum number for page is 1" })
  @Type(() => Number)
  page?: number;

  @ApiProperty({ required: false, default: 10, maximum: 1000 })
  @IsOptional()
  @IsPositive()
  @Min(1, { message: "Minimum number for limit is 1" })
  @Max(1000, { message: "Maximum number for limit is 1000" })
  @Type(() => Number)
  limit?: number;

  @ApiProperty({
    description: "[field] (Ascending) or -[field] (Descending), multiple sort split by ; (ex: -updatedAt;-storeName)",
    required: false,
  })
  @Transform(({ value }) => value.replace(";", " "))
  @IsOptional()
  @IsString()
  sort?: string;
}
