import {
  registerDecorator,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  ValidationArguments,
} from "class-validator";

@ValidatorConstraint()
export class TimezoneOffsetConstraint implements ValidatorConstraintInterface {
  validate(date: string, args: ValidationArguments) {
    return /^(-|\+)(0[0-9]|1[0-4]):(00|30|45)$/.test(date);
  }

  defaultMessage(args: ValidationArguments) {
    return `${args.property} not a valid timezone offset format`;
  }
}

export function TimezoneOffset(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [],
      validator: TimezoneOffsetConstraint,
    });
  };
}
