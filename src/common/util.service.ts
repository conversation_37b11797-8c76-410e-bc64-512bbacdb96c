/*
https://docs.nestjs.com/providers#services
*/

import { Injectable } from "@nestjs/common";
import { isArray, isEmail, isObject } from "class-validator";

@Injectable()
export class UtilService {
  async checkIdentifier(identifier: string) {
    let email = "";
    let phoneNumber = "";
    let cardNumber = "";
    let query: any;
    if (identifier.match("^\\+?\\d+$")) {
      phoneNumber = identifier.toString().replace(/^0/g, "62");
      query = { phoneNumber: phoneNumber };
    } else if (isEmail(identifier)) {
      email = identifier;
      query = { email: email };
    } else {
      cardNumber = identifier;
      query = { cardNumber: cardNumber };
    }

    return { email: email, phoneNumber: phoneNumber, cardNumber: cardNumber, query: query };
  }

  toCamel = (s) => {
    return s.replace(/([-_][a-z])/gi, ($1) => {
      return $1.toUpperCase().replace("-", "").replace("_", "");
    });
  };

  keysToCamel(o: any) {
    if (isObject(o)) {
      const n = {};
      Object.keys(o).forEach((k) => {
        n[this.toCamel(k)] = this.keysToCamel(o[k]);
      });
      return n;
    } else if (isArray(o)) {
      return o.map((i) => {
        return this.keysToCamel(i);
      });
    }
    return o;
  }
}
