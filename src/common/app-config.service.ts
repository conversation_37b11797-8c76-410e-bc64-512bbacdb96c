/*
https://docs.nestjs.com/providers#services
*/

import { Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";

@Injectable()
export class AppConfigService {
  private readonly _mongoConnectionString;

  constructor(private readonly configService: ConfigService) {
    this._mongoConnectionString = this._getConnectionStringFromEnvFile();
  }

  get connectionString() {
    return this._mongoConnectionString;
  }

  private async _getConnectionStringFromEnvFile(): Promise<string> {
    const mongoUrl = this.configService.get<string>("MONGO_URL");
    const connectionString = `mongodb://${mongoUrl}`;
    if (!connectionString) {
      throw new Error("No connection string has been provided in the .env file.");
    }
    return connectionString;
  }
}
