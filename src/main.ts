import { Logger, ValidationPipe, VersioningType } from "@nestjs/common";
import { NestFactory } from "@nestjs/core";
import { DocumentBuilder, SwaggerModule } from "@nestjs/swagger";
import { AppModule } from "./app.module";
import { ResponseInterceptor } from "./response.interceptor";
import { customOriginValidation } from "./utils/custom-origin-validation";
import { KeycloakGeneratorService } from "./module/keycloak-generator/keycloak-generator.service";
import { KeycloakGeneratorController } from "./module/keycloak-generator/keycloak-generator.controller";

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  app.enableCors({
    methods: process.env.CORS_METHOD,
    origin: customOriginValidation,
  });
  app.useGlobalPipes(new ValidationPipe({ always: true, stopAtFirstError: true, transform: true }));
  app.useGlobalInterceptors(new ResponseInterceptor());
  app.setGlobalPrefix("api");
  app.enableVersioning({
    type: VersioningType.URI,
    defaultVersion: "1",
  });

  const config = new DocumentBuilder()
    .setTitle("TBS Microservice API")
    .setDescription("The Body Shop : Utilities Microservice API")
    .setVersion("0.1")
    .addBearerAuth(
      {
        // I was also testing it without prefix 'Bearer ' before the JWT
        description: `[just text field] Please enter token in following format: Bearer <JWT>`,
        name: "Authorization",
        bearerFormat: "Bearer", // I`ve tested not to use this field, but the result was the same
        scheme: "Bearer",
        type: "http", // I`ve attempted type: 'apiKey' too
        in: "Header",
      },
      "access-token", // This name here is important for matching up with @ApiBearerAuth() in your controller!
    )
    .build();

  const document = SwaggerModule.createDocument(app, config);

  const service = app.get(KeycloakGeneratorService);
  new KeycloakGeneratorController(service).generate(Object.values(document.paths)).then(() => {
    new Logger("Keycloak Generator").verbose("Successfully Synchronize Permission and Resource");
  });

  SwaggerModule.setup("docs", app, document, {
    swaggerOptions: {
      docExpansion: "none",
      persistAuthorization: true,
      displayRequestDuration: true,
    },
  });

  await app.startAllMicroservices();

  await app.listen(process.env.APPS_PORT || 3000).then(() => {
    console.log("Running on port: " + process.env.APPS_PORT);
  });
}
bootstrap();
