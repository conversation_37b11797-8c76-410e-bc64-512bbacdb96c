{
    "extends": "next",
    "rules": {
        "react/no-unescaped-entities": "off",
        "@next/next/no-page-custom-font": "off",
        "no-unused-vars": 1,
        "import/no-unresolved": 1,
        "no-console": [
            "warn",
            {
                "allow": [
                    "info",
                    "warn",
                    "error",
                    "debug"
                ]
            }
        ],
        "no-plusplus": 0,
        "prefer-destructuring": [
            "warn",
            {
                "object": true,
                "array": false
            }
        ],
        "no-underscore-dangle": 0,
        "@next/next/no-img-element": 0,
        "no-sparse-arrays": 0,
        "react-hooks/exhaustive-deps": [
            "warn",
            {
                "additionalHooks": "(useFastRefreshEffect|useSlowRefreshEffect)"
            }
        ],
        // Start temporary rules
        // These rules are here just to keep the lint error to 0 during the migration to the new rule set
        // They need to be removed and fixed as soon as possible
        // "@typescript-eslint/ban-ts-comment": [1, { "ts-ignore": false, "ts-nocheck": false }],
        "@typescript-eslint/no-use-before-define": 0,
        "@typescript-eslint/explicit-module-boundary-types": 0,
        "@typescript-eslint/no-explicit-any": 0,
        "radix": 0,
        "import/no-extraneous-dependencies": 0,
        "jsx-a11y/media-has-caption": 0,
        // Exchange
        // "no-param-reassign": ["error", { "props": true, "ignorePropertyModificationsFor": ["state", "memo", "config"] }],
        "react/require-default-props": 0,
        "no-nested-ternary": 0,
        "max-classes-per-file": 0
        // End temporary rules
    }
}